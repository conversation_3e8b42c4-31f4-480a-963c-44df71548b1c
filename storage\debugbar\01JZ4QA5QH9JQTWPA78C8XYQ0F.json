{"__meta": {"id": "01JZ4QA5QH9JQTWPA78C8XYQ0F", "datetime": "2025-07-02 04:46:23", "utime": **********.474175, "method": "GET", "uri": "/user/referral/tree", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751431582.900307, "end": **********.474198, "duration": 0.5738911628723145, "duration_str": "574ms", "measures": [{"label": "Booting", "start": 1751431582.900307, "relative_start": 0, "end": **********.107277, "relative_end": **********.107277, "duration": 0.****************, "duration_str": "207ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.107286, "relative_start": 0.*****************, "end": **********.4742, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.113398, "relative_start": 0.*****************, "end": **********.115898, "relative_end": **********.115898, "duration": 0.002499818801879883, "duration_str": "2.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.150798, "relative_start": 0.****************, "end": **********.472834, "relative_end": **********.472834, "duration": 0.****************, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 39, "nb_templates": 39, "templates": [{"name": "1x frontend::referral.tree", "param_count": null, "params": [], "start": **********.157148, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.phpfrontend::referral.tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Ftree.blade.php:1", "ajax": false, "filename": "tree.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::referral.tree"}, {"name": "9x frontend::referral.include.__tree", "param_count": null, "params": [], "start": **********.166558, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.phpfrontend::referral.include.__tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:1", "ajax": false, "filename": "__tree.blade.php", "line": "?"}, "render_count": 9, "name_original": "frontend::referral.include.__tree"}, {"name": "1x frontend::layouts.user", "param_count": null, "params": [], "start": **********.235414, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::layouts.user"}, {"name": "1x frontend::user.include.__head", "param_count": null, "params": [], "start": **********.24255, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.phpfrontend::user.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.258292, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x frontend::include.__user_side_nav", "param_count": null, "params": [], "start": **********.264749, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.phpfrontend::include.__user_side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:1", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_side_nav"}, {"name": "18x frontend::include.__menu-item", "param_count": null, "params": [], "start": **********.289273, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__menu-item.blade.phpfrontend::include.__menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__menu-item.blade.php:1", "ajax": false, "filename": "__menu-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "frontend::include.__menu-item"}, {"name": "1x frontend::include.__user_header", "param_count": null, "params": [], "start": **********.415389, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.phpfrontend::include.__user_header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:1", "ajax": false, "filename": "__user_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_header"}, {"name": "1x frontend.default.include.__user-notification-data", "param_count": null, "params": [], "start": **********.438196, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/frontend/default/include/__user-notification-data.blade.phpfrontend.default.include.__user-notification-data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F__user-notification-data.blade.php:1", "ajax": false, "filename": "__user-notification-data.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.default.include.__user-notification-data"}, {"name": "1x frontend::include.__kyc_warning", "param_count": null, "params": [], "start": **********.447514, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__kyc_warning.blade.phpfrontend::include.__kyc_warning", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__kyc_warning.blade.php:1", "ajax": false, "filename": "__kyc_warning.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__kyc_warning"}, {"name": "1x frontend::include.__matrix-activation", "param_count": null, "params": [], "start": **********.452734, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__matrix-activation.blade.phpfrontend::include.__matrix-activation", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__matrix-activation.blade.php:1", "ajax": false, "filename": "__matrix-activation.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__matrix-activation"}, {"name": "1x frontend::user.include.__script", "param_count": null, "params": [], "start": **********.457741, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__script.blade.phpfrontend::user.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__script"}, {"name": "1x global.__t_notify", "param_count": null, "params": [], "start": **********.465279, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__t_notify.blade.phpglobal.__t_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__t_notify.blade.php:1", "ajax": false, "filename": "__t_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__t_notify"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.469961, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET user/referral/tree", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, App\\Http\\Middleware\\AllowIframeMiddleware", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "user.referral.tree", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:30-35</a>"}, "queries": {"count": 26, "nb_statements": 26, "nb_visible_statements": 26, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "31.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 228 limit 1", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.121325, "duration": 0.01391, "duration_str": "13.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 43.922}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 228 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [228, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "2fa", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\TwoFaCheck.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "install_check", "file": "E:\\laragon\\www\\revoaibank\\vendor\\remotelywork\\installer\\src\\Http\\Middleware\\InstallCheck.php", "line": 17}], "start": **********.1439621, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "isActive:23", "source": {"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FMiddleware%2FCheckDeactivate.php:23", "ajax": false, "filename": "CheckDeactivate.php", "line": "23"}, "connection": "revoaibank", "explain": null, "start_percent": 43.922, "width_percent": 10.42}, {"sql": "select max(`the_order`) as aggregate from `level_referrals`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 32}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1481931, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ReferralController.php:32", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:32", "ajax": false, "filename": "ReferralController.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 54.342, "width_percent": 4.389}, {"sql": "select * from `users` where `users`.`ref_id` = 228 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.php", "line": 18}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.160407, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.tree:18", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.php", "line": 18}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Ftree.blade.php:18", "ajax": false, "filename": "tree.blade.php", "line": "18"}, "connection": "revoaibank", "explain": null, "start_percent": 58.731, "width_percent": 1.989}, {"sql": "select * from `users` where `users`.`ref_id` = 229 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [229], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.173358, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 60.72, "width_percent": 2.21}, {"sql": "select * from `users` where `users`.`ref_id` = 231 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [231], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.1813922, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 62.93, "width_percent": 2.021}, {"sql": "select * from `users` where `users`.`ref_id` = 232 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [232], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.189442, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 64.951, "width_percent": 1.705}, {"sql": "select * from `users` where `users`.`ref_id` = 233 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [233], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.197427, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 66.656, "width_percent": 1.958}, {"sql": "select * from `users` where `users`.`ref_id` = 234 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [234], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.204941, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 68.614, "width_percent": 1.579}, {"sql": "select * from `users` where `users`.`ref_id` = 235 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [235], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.212281, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 70.193, "width_percent": 2.21}, {"sql": "select * from `users` where `users`.`ref_id` = 236 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [236], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2205832, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 72.403, "width_percent": 1.989}, {"sql": "select * from `users` where `users`.`ref_id` = 237 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [237], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.228325, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 74.392, "width_percent": 2.242}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.236125, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "helpers.php:636", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:636", "ajax": false, "filename": "helpers.php", "line": "636"}, "connection": "revoaibank", "explain": null, "start_percent": 76.634, "width_percent": 2.4}, {"sql": "select * from `custom_css` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.247912, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "frontend::user.include.__head:24", "source": {"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:24", "ajax": false, "filename": "__head.blade.php", "line": "24"}, "connection": "revoaibank", "explain": null, "start_percent": 79.034, "width_percent": 2.4}, {"sql": "select count(*) as aggregate from `dps` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.277092, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "helpers.php:673", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:673", "ajax": false, "filename": "helpers.php", "line": "673"}, "connection": "revoaibank", "explain": null, "start_percent": 81.434, "width_percent": 2.463}, {"sql": "select count(*) as aggregate from `fdr` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.278752, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "helpers.php:681", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:681", "ajax": false, "filename": "helpers.php", "line": "681"}, "connection": "revoaibank", "explain": null, "start_percent": 83.896, "width_percent": 2.526}, {"sql": "select count(*) as aggregate from `loans` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2805161, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:690", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:690", "ajax": false, "filename": "helpers.php", "line": "690"}, "connection": "revoaibank", "explain": null, "start_percent": 86.422, "width_percent": 2.589}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `user_id` = 228", "type": "query", "params": [], "bindings": ["open", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.282574, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:13", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:13", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "13"}, "connection": "revoaibank", "explain": null, "start_percent": 89.012, "width_percent": 1.01}, {"sql": "select * from `user_navigations` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2835588, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:15", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:15", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "15"}, "connection": "revoaibank", "explain": null, "start_percent": 90.022, "width_percent": 1.2}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 228 order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["user", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.430326, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:32", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:32", "ajax": false, "filename": "__user_header.blade.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 91.222, "width_percent": 2.779}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 228 and `read` = 0", "type": "query", "params": [], "bindings": ["user", 228, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4317799, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:33", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:33", "ajax": false, "filename": "__user_header.blade.php", "line": "33"}, "connection": "revoaibank", "explain": null, "start_percent": 94.001, "width_percent": 1.2}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 228", "type": "query", "params": [], "bindings": ["user", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4326282, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:34", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:34", "ajax": false, "filename": "__user_header.blade.php", "line": "34"}, "connection": "revoaibank", "explain": null, "start_percent": 95.201, "width_percent": 0.916}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4407542, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:45", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:45", "ajax": false, "filename": "__user_header.blade.php", "line": "45"}, "connection": "revoaibank", "explain": null, "start_percent": 96.116, "width_percent": 0.853}, {"sql": "select * from `plugins` where `name` = 'Google Analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Google Analytics", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4703681, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 96.969, "width_percent": 1.516}, {"sql": "select * from `plugins` where `name` = '<PERSON><PERSON><PERSON>' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Tawk Chat", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4714038, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 98.484, "width_percent": 0.758}, {"sql": "select * from `plugins` where `name` = 'Facebook Messenger' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Facebook Messenger", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4720569, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 99.242, "width_percent": 0.758}]}, "models": {"data": {"App\\Models\\UserNavigation": {"value": 18, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserNavigation.php:1", "ajax": false, "filename": "UserNavigation.php", "line": "?"}}, "App\\Models\\User": {"value": 9, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\CustomCss": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCustomCss.php:1", "ajax": false, "filename": "CustomCss.php", "line": "?"}}}, "count": 30, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/user/referral\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "228", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral/tree", "action_name": "user.referral.tree", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree", "uri": "GET user/referral/tree", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:30-35</a>", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, web, web, App\\Http\\Middleware\\AllowIframeMiddleware", "duration": "577ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6Ik1vaXhMait5NTg2OEkrWkRiRmhnOUE9PSIsInZhbHVlIjoiR0pjclJQM2UvUFpodXUxMkNyYVJ0OFBTVUxUQkRNZEhyMmg3MDBHUUsxaVNSdXA4dFhOWEVrWG85SGJnWjEvc0g4MlhIOWN3MktVZXNIN25FSWpDbEdtdDhBMFdHV29sd2VGQzRkSmtob2ttdmxIa05HSXVRM0ZPT3RwUzduV0siLCJtYWMiOiI5ZjYzOTJkMGM4ZGU3NjQyNDYzZDcyZDQ2ZmFkNDk5MWZmNzFiMTMxOTVhMzNmY2E1NjM5ZjRkM2Q3ZGRiMGE1IiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6IjlSanZPc0dranZHWTZ5NnUzK1VXVHc9PSIsInZhbHVlIjoicDhadHdCd3ZsS2RtdTFNTnpRall1eWRVczl1a0UwMG9RNmJWaGZJL1IrNmptMlVjdjdlaE9mdS9mcHRFZ0ZmdTJqQU9yaXZ1UWhQcW9VSW9nVXRDSVNoaXZyd0dGU2ErelA5bTdpSUlqT0tLSEd3VTNTUGR0M3J5YkRxWU9xVzkiLCJtYWMiOiJjZDc3NWIyNGJmMzA5ZTNhYTVhMWQxYzdmZDg0YTFlNjAzNzNjZjg5ODU1Yjc4NGE2NzhlZDQ2ZmM5NTdlNjNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://revoaibank.test/user/referral</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LQmddLR34V5gx8fxxD8s2W20y4FntkShwkOAEGlR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:46:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-frame-options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">ALLOWALL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://revoaibank.test/user/referral</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>228</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral/tree", "action_name": "user.referral.tree", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree"}, "badge": null}}