{"__meta": {"id": "01JZ4QNC0ZDZD0DN6XEP3BBXBZ", "datetime": "2025-07-02 04:52:30", "utime": **********.368138, "method": "GET", "uri": "/user/referral/tree", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751431949.820621, "end": **********.36816, "duration": 0.5475389957427979, "duration_str": "548ms", "measures": [{"label": "Booting", "start": 1751431949.820621, "relative_start": 0, "end": **********.028379, "relative_end": **********.028379, "duration": 0.*****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.028388, "relative_start": 0.*****************, "end": **********.368162, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "340ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.03481, "relative_start": 0.****************, "end": **********.0373, "relative_end": **********.0373, "duration": 0.0024900436401367188, "duration_str": "2.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.05547, "relative_start": 0.****************, "end": **********.366824, "relative_end": **********.366824, "duration": 0.****************, "duration_str": "311ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 43, "nb_templates": 43, "templates": [{"name": "1x frontend::referral.tree", "param_count": null, "params": [], "start": **********.061836, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.phpfrontend::referral.tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Ftree.blade.php:1", "ajax": false, "filename": "tree.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::referral.tree"}, {"name": "13x frontend::referral.include.__tree", "param_count": null, "params": [], "start": **********.06981, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.phpfrontend::referral.include.__tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:1", "ajax": false, "filename": "__tree.blade.php", "line": "?"}, "render_count": 13, "name_original": "frontend::referral.include.__tree"}, {"name": "1x frontend::layouts.user", "param_count": null, "params": [], "start": **********.146725, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::layouts.user"}, {"name": "1x frontend::user.include.__head", "param_count": null, "params": [], "start": **********.153318, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.phpfrontend::user.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.168921, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x frontend::include.__user_side_nav", "param_count": null, "params": [], "start": **********.174275, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.phpfrontend::include.__user_side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:1", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_side_nav"}, {"name": "18x frontend::include.__menu-item", "param_count": null, "params": [], "start": **********.195949, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__menu-item.blade.phpfrontend::include.__menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__menu-item.blade.php:1", "ajax": false, "filename": "__menu-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "frontend::include.__menu-item"}, {"name": "1x frontend::include.__user_header", "param_count": null, "params": [], "start": **********.311541, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.phpfrontend::include.__user_header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:1", "ajax": false, "filename": "__user_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_header"}, {"name": "1x frontend.default.include.__user-notification-data", "param_count": null, "params": [], "start": **********.333263, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/frontend/default/include/__user-notification-data.blade.phpfrontend.default.include.__user-notification-data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F__user-notification-data.blade.php:1", "ajax": false, "filename": "__user-notification-data.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.default.include.__user-notification-data"}, {"name": "1x frontend::include.__kyc_warning", "param_count": null, "params": [], "start": **********.341972, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__kyc_warning.blade.phpfrontend::include.__kyc_warning", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__kyc_warning.blade.php:1", "ajax": false, "filename": "__kyc_warning.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__kyc_warning"}, {"name": "1x frontend::include.__matrix-activation", "param_count": null, "params": [], "start": **********.347258, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__matrix-activation.blade.phpfrontend::include.__matrix-activation", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__matrix-activation.blade.php:1", "ajax": false, "filename": "__matrix-activation.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__matrix-activation"}, {"name": "1x frontend::user.include.__script", "param_count": null, "params": [], "start": **********.352355, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__script.blade.phpfrontend::user.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__script"}, {"name": "1x global.__t_notify", "param_count": null, "params": [], "start": **********.359349, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__t_notify.blade.phpglobal.__t_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__t_notify.blade.php:1", "ajax": false, "filename": "__t_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__t_notify"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.364008, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET user/referral/tree", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, App\\Http\\Middleware\\AllowIframeMiddleware", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "user.referral.tree", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:30-35</a>"}, "queries": {"count": 30, "nb_statements": 30, "nb_visible_statements": 30, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0144, "accumulated_duration_str": "14.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 254 limit 1", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.04305, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 8.611}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 254 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [254, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "2fa", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\TwoFaCheck.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "install_check", "file": "E:\\laragon\\www\\revoaibank\\vendor\\remotelywork\\installer\\src\\Http\\Middleware\\InstallCheck.php", "line": 17}], "start": **********.052561, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "isActive:23", "source": {"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FMiddleware%2FCheckDeactivate.php:23", "ajax": false, "filename": "CheckDeactivate.php", "line": "23"}, "connection": "revoaibank", "explain": null, "start_percent": 8.611, "width_percent": 4.653}, {"sql": "select max(`the_order`) as aggregate from `level_referrals`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 32}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.053972, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ReferralController.php:32", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:32", "ajax": false, "filename": "ReferralController.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 13.264, "width_percent": 2.292}, {"sql": "select * from `users` where `users`.`ref_id` = 254 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.php", "line": 18}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.064009, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.tree:18", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/tree.blade.php", "line": 18}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Ftree.blade.php:18", "ajax": false, "filename": "tree.blade.php", "line": "18"}, "connection": "revoaibank", "explain": null, "start_percent": 15.556, "width_percent": 3.889}, {"sql": "select * from `users` where `users`.`ref_id` = 255 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [255], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.075205, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 19.444, "width_percent": 6.181}, {"sql": "select * from `users` where `users`.`ref_id` = 256 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [256], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.082415, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 25.625, "width_percent": 3.333}, {"sql": "select * from `users` where `users`.`ref_id` = 264 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [264], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.088351, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 28.958, "width_percent": 2.361}, {"sql": "select * from `users` where `users`.`ref_id` = 265 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [265], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.094495, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 31.319, "width_percent": 3.542}, {"sql": "select * from `users` where `users`.`ref_id` = 266 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [266], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.100407, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 34.861, "width_percent": 2.5}, {"sql": "select * from `users` where `users`.`ref_id` = 257 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [257], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.105968, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 37.361, "width_percent": 2.083}, {"sql": "select * from `users` where `users`.`ref_id` = 258 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [258], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.111962, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 39.444, "width_percent": 3.819}, {"sql": "select * from `users` where `users`.`ref_id` = 259 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [259], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.117929, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 43.264, "width_percent": 2.361}, {"sql": "select * from `users` where `users`.`ref_id` = 260 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [260], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.123582, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 45.625, "width_percent": 2.5}, {"sql": "select * from `users` where `users`.`ref_id` = 261 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [261], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.1294801, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 48.125, "width_percent": 3.403}, {"sql": "select * from `users` where `users`.`ref_id` = 262 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [262], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.13541, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 51.528, "width_percent": 2.292}, {"sql": "select * from `users` where `users`.`ref_id` = 263 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [263], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.141029, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.include.__tree:25", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.include.__tree", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/include/__tree.blade.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Finclude%2F__tree.blade.php:25", "ajax": false, "filename": "__tree.blade.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 53.819, "width_percent": 2.5}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.147355, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "helpers.php:636", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:636", "ajax": false, "filename": "helpers.php", "line": "636"}, "connection": "revoaibank", "explain": null, "start_percent": 56.319, "width_percent": 4.653}, {"sql": "select * from `custom_css` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.158493, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "frontend::user.include.__head:24", "source": {"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:24", "ajax": false, "filename": "__head.blade.php", "line": "24"}, "connection": "revoaibank", "explain": null, "start_percent": 60.972, "width_percent": 5.139}, {"sql": "select count(*) as aggregate from `dps` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.184521, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "helpers.php:673", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:673", "ajax": false, "filename": "helpers.php", "line": "673"}, "connection": "revoaibank", "explain": null, "start_percent": 66.111, "width_percent": 4.722}, {"sql": "select count(*) as aggregate from `fdr` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.186042, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "helpers.php:681", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:681", "ajax": false, "filename": "helpers.php", "line": "681"}, "connection": "revoaibank", "explain": null, "start_percent": 70.833, "width_percent": 2.639}, {"sql": "select count(*) as aggregate from `loans` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.187192, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "helpers.php:690", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:690", "ajax": false, "filename": "helpers.php", "line": "690"}, "connection": "revoaibank", "explain": null, "start_percent": 73.472, "width_percent": 2.639}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `user_id` = 254", "type": "query", "params": [], "bindings": ["open", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.189017, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:13", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:13", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "13"}, "connection": "revoaibank", "explain": null, "start_percent": 76.111, "width_percent": 2.5}, {"sql": "select * from `user_navigations` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.189945, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:15", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:15", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "15"}, "connection": "revoaibank", "explain": null, "start_percent": 78.611, "width_percent": 2.847}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 254 order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["user", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3255272, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:32", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:32", "ajax": false, "filename": "__user_header.blade.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 81.458, "width_percent": 5.625}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 254 and `read` = 0", "type": "query", "params": [], "bindings": ["user", 254, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.326898, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:33", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:33", "ajax": false, "filename": "__user_header.blade.php", "line": "33"}, "connection": "revoaibank", "explain": null, "start_percent": 87.083, "width_percent": 2.083}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 254", "type": "query", "params": [], "bindings": ["user", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.327642, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:34", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:34", "ajax": false, "filename": "__user_header.blade.php", "line": "34"}, "connection": "revoaibank", "explain": null, "start_percent": 89.167, "width_percent": 2.083}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.3357852, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:45", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:45", "ajax": false, "filename": "__user_header.blade.php", "line": "45"}, "connection": "revoaibank", "explain": null, "start_percent": 91.25, "width_percent": 2.083}, {"sql": "select * from `plugins` where `name` = 'Google Analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Google Analytics", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.364412, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 93.333, "width_percent": 3.333}, {"sql": "select * from `plugins` where `name` = '<PERSON><PERSON><PERSON>' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Tawk Chat", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.365392, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 96.667, "width_percent": 1.667}, {"sql": "select * from `plugins` where `name` = 'Facebook Messenger' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Facebook Messenger", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.366046, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 98.333, "width_percent": 1.667}]}, "models": {"data": {"App\\Models\\UserNavigation": {"value": 18, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserNavigation.php:1", "ajax": false, "filename": "UserNavigation.php", "line": "?"}}, "App\\Models\\User": {"value": 13, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\CustomCss": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCustomCss.php:1", "ajax": false, "filename": "CustomCss.php", "line": "?"}}}, "count": 34, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/user/referral\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://revoaibank.test/user/referral/tree\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "254", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral/tree", "action_name": "user.referral.tree", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree", "uri": "GET user/referral/tree", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:30-35</a>", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, web, web, App\\Http\\Middleware\\AllowIframeMiddleware", "duration": "550ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IkxDbksvVlE0dHVYb1M1aG9oUk8yVkE9PSIsInZhbHVlIjoidUhZWlhzQVlqa25LZTNmaERjaWhqZUdFNWgxaysrWHpzYWtBNDd5a0xodWUycXFPNHI3Z01wSWtVbnBNMEt2d3BDL2VhWXVxUEVLR3ZIYmM5bWFrWmFSYTVIRzVvc3FaMWtMTjd5bjJxU1ZHdmFMSk9USDM1WFFEdS9yL1BSQ2QiLCJtYWMiOiJlMTliMTJjMjMxNDRmNjFhMDUwN2FjMjQwYTQ4NWQxMzJhZDBhMGUxMGU3NWE1ZTg4OTJmZGIzMTY2Yjc1Mzg3IiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6IlphTW1tMXo4M0tmTEhJQkRvNnhwelE9PSIsInZhbHVlIjoiY0V5bGxKRy8rMUkyQ3JEMjdHaGc0cFJaaEhmNzM2dFZFZGtRODZhK1dXeEk0cDFlSUthRDY5MWtGWks2bEZTTmN5KzR6b3l3L2svblRyZHZkSGNxcWZXWklweG9XTjFXeTc1dXNPSDd2aW1TNlFFNkFFMnI2cnZ4c2lhRDBjNksiLCJtYWMiOiIwNWFmYzc0N2Q2ZjAwZmU1NjU2MTMzYjVkMzZiNzNmY2M0YjZkMTE2YjVkODc5ZGJmNDI2MjZhOWMyYjBiNDRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://revoaibank.test/user/referral</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BBpNnebL7ri3iVl8H6kWjImZ7MyO5U5HwS6wczTX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:52:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-frame-options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">ALLOWALL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://revoaibank.test/user/referral</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/user/referral/tree</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>254</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral/tree", "action_name": "user.referral.tree", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referralTree"}, "badge": null}}