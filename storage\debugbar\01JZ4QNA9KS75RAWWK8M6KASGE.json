{"__meta": {"id": "01JZ4QNA9KS75RAWWK8M6KASGE", "datetime": "2025-07-02 04:52:28", "utime": **********.596218, "method": "GET", "uri": "/user/referral", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.117834, "end": **********.596244, "duration": 0.478410005569458, "duration_str": "478ms", "measures": [{"label": "Booting", "start": **********.117834, "relative_start": 0, "end": **********.319927, "relative_end": **********.319927, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.319935, "relative_start": 0.****************, "end": **********.596246, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "276ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.32583, "relative_start": 0.*****************, "end": **********.328276, "relative_end": **********.328276, "duration": 0.0024459362030029297, "duration_str": "2.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.352545, "relative_start": 0.****************, "end": **********.594774, "relative_end": **********.594774, "duration": 0.*****************, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 30, "nb_templates": 30, "templates": [{"name": "1x frontend::referral.index", "param_count": null, "params": [], "start": **********.359028, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.phpfrontend::referral.index", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::referral.index"}, {"name": "1x frontend::layouts.user", "param_count": null, "params": [], "start": **********.374913, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::layouts.user"}, {"name": "1x frontend::user.include.__head", "param_count": null, "params": [], "start": **********.381907, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.phpfrontend::user.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.396743, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x frontend::include.__user_side_nav", "param_count": null, "params": [], "start": **********.402196, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.phpfrontend::include.__user_side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:1", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_side_nav"}, {"name": "18x frontend::include.__menu-item", "param_count": null, "params": [], "start": **********.424182, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__menu-item.blade.phpfrontend::include.__menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__menu-item.blade.php:1", "ajax": false, "filename": "__menu-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "frontend::include.__menu-item"}, {"name": "1x frontend::include.__user_header", "param_count": null, "params": [], "start": **********.540131, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.phpfrontend::include.__user_header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:1", "ajax": false, "filename": "__user_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_header"}, {"name": "1x frontend.default.include.__user-notification-data", "param_count": null, "params": [], "start": **********.561712, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/frontend/default/include/__user-notification-data.blade.phpfrontend.default.include.__user-notification-data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F__user-notification-data.blade.php:1", "ajax": false, "filename": "__user-notification-data.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.default.include.__user-notification-data"}, {"name": "1x frontend::include.__kyc_warning", "param_count": null, "params": [], "start": **********.570174, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__kyc_warning.blade.phpfrontend::include.__kyc_warning", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__kyc_warning.blade.php:1", "ajax": false, "filename": "__kyc_warning.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__kyc_warning"}, {"name": "1x frontend::include.__matrix-activation", "param_count": null, "params": [], "start": **********.57501, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__matrix-activation.blade.phpfrontend::include.__matrix-activation", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__matrix-activation.blade.php:1", "ajax": false, "filename": "__matrix-activation.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__matrix-activation"}, {"name": "1x frontend::user.include.__script", "param_count": null, "params": [], "start": **********.579951, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__script.blade.phpfrontend::user.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__script"}, {"name": "1x global.__t_notify", "param_count": null, "params": [], "start": **********.58682, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__t_notify.blade.phpglobal.__t_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__t_notify.blade.php:1", "ajax": false, "filename": "__t_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__t_notify"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.591331, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET user/referral", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, App\\Http\\Middleware\\AllowIframeMiddleware", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referral<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:11\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "user.referral", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:11\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:11-28</a>"}, "queries": {"count": 23, "nb_statements": 23, "nb_visible_statements": 23, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.011619999999999998, "accumulated_duration_str": "11.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 254 limit 1", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.333828, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 4.991}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 254 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [254, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "2fa", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\TwoFaCheck.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "install_check", "file": "E:\\laragon\\www\\revoaibank\\vendor\\remotelywork\\installer\\src\\Http\\Middleware\\InstallCheck.php", "line": 17}], "start": **********.342544, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "isActive:23", "source": {"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FMiddleware%2FCheckDeactivate.php:23", "ajax": false, "filename": "CheckDeactivate.php", "line": "23"}, "connection": "revoaibank", "explain": null, "start_percent": 4.991, "width_percent": 9.036}, {"sql": "select * from `referral_programs`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.34706, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "User.php:244", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:244", "ajax": false, "filename": "User.php", "line": "244"}, "connection": "revoaibank", "explain": null, "start_percent": 14.028, "width_percent": 6.196}, {"sql": "select * from `referral_links` where (`user_id` = 254 and `referral_program_id` = 1) limit 1", "type": "query", "params": [], "bindings": [254, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 21}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.348767, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ReferralLink.php:17", "source": {"index": 20, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralLink.php:17", "ajax": false, "filename": "ReferralLink.php", "line": "17"}, "connection": "revoaibank", "explain": null, "start_percent": 20.224, "width_percent": 4.819}, {"sql": "select max(`the_order`) as aggregate from `level_referrals`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 23}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.349972, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "ReferralController.php:23", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 23}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:23", "ajax": false, "filename": "ReferralController.php", "line": "23"}, "connection": "revoaibank", "explain": null, "start_percent": 25.043, "width_percent": 2.151}, {"sql": "select * from `settings` where `name` = 'referral_rules' limit 1", "type": "query", "params": [], "bindings": ["referral_rules"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3508148, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ReferralController.php:25", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ReferralController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\ReferralController.php", "line": 25}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:25", "ajax": false, "filename": "ReferralController.php", "line": "25"}, "connection": "revoaibank", "explain": null, "start_percent": 27.194, "width_percent": 3.701}, {"sql": "select * from `referral_programs` where `referral_programs`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 38}, {"index": 27, "namespace": "view", "name": "frontend::referral.index", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.php", "line": 24}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.3636508, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ReferralLink.php:38", "source": {"index": 21, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 38}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralLink.php:38", "ajax": false, "filename": "ReferralLink.php", "line": "38"}, "connection": "revoaibank", "explain": null, "start_percent": 30.895, "width_percent": 5.077}, {"sql": "select count(*) as aggregate from `referral_relationships` where `referral_relationships`.`referral_link_id` = 24 and `referral_relationships`.`referral_link_id` is not null", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend::referral.index", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.php", "line": 30}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.365226, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.index:30", "source": {"index": 19, "namespace": "view", "name": "frontend::referral.index", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.php", "line": 30}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Findex.blade.php:30", "ajax": false, "filename": "index.blade.php", "line": "30"}, "connection": "revoaibank", "explain": null, "start_percent": 35.972, "width_percent": 3.873}, {"sql": "select * from `users` where `users`.`ref_id` = 254 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::referral.index", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.php", "line": 131}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.36892, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::referral.index:131", "source": {"index": 20, "namespace": "view", "name": "frontend::referral.index", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/referral/index.blade.php", "line": 131}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Freferral%2Findex.blade.php:131", "ajax": false, "filename": "index.blade.php", "line": "131"}, "connection": "revoaibank", "explain": null, "start_percent": 39.845, "width_percent": 3.098}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.375631, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "helpers.php:636", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:636", "ajax": false, "filename": "helpers.php", "line": "636"}, "connection": "revoaibank", "explain": null, "start_percent": 42.943, "width_percent": 5.422}, {"sql": "select * from `custom_css` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.386838, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "frontend::user.include.__head:24", "source": {"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:24", "ajax": false, "filename": "__head.blade.php", "line": "24"}, "connection": "revoaibank", "explain": null, "start_percent": 48.365, "width_percent": 4.561}, {"sql": "select count(*) as aggregate from `dps` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.412374, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "helpers.php:673", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:673", "ajax": false, "filename": "helpers.php", "line": "673"}, "connection": "revoaibank", "explain": null, "start_percent": 52.926, "width_percent": 7.487}, {"sql": "select count(*) as aggregate from `fdr` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4141839, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "helpers.php:681", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:681", "ajax": false, "filename": "helpers.php", "line": "681"}, "connection": "revoaibank", "explain": null, "start_percent": 60.413, "width_percent": 3.701}, {"sql": "select count(*) as aggregate from `loans` where `status` = 'running' and `user_id` = 254", "type": "query", "params": [], "bindings": ["running", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.4154758, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "helpers.php:690", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:690", "ajax": false, "filename": "helpers.php", "line": "690"}, "connection": "revoaibank", "explain": null, "start_percent": 64.114, "width_percent": 3.27}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `user_id` = 254", "type": "query", "params": [], "bindings": ["open", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.417119, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:13", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:13", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "13"}, "connection": "revoaibank", "explain": null, "start_percent": 67.384, "width_percent": 3.098}, {"sql": "select * from `user_navigations` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.418069, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:15", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:15", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "15"}, "connection": "revoaibank", "explain": null, "start_percent": 70.482, "width_percent": 3.442}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 254 order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["user", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.554165, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:32", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:32", "ajax": false, "filename": "__user_header.blade.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 73.924, "width_percent": 6.368}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 254 and `read` = 0", "type": "query", "params": [], "bindings": ["user", 254, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.555442, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:33", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:33", "ajax": false, "filename": "__user_header.blade.php", "line": "33"}, "connection": "revoaibank", "explain": null, "start_percent": 80.293, "width_percent": 3.959}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 254", "type": "query", "params": [], "bindings": ["user", 254], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.556308, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:34", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:34", "ajax": false, "filename": "__user_header.blade.php", "line": "34"}, "connection": "revoaibank", "explain": null, "start_percent": 84.251, "width_percent": 2.496}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.564244, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:45", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:45", "ajax": false, "filename": "__user_header.blade.php", "line": "45"}, "connection": "revoaibank", "explain": null, "start_percent": 86.747, "width_percent": 2.324}, {"sql": "select * from `plugins` where `name` = 'Google Analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Google Analytics", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5917149, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 89.071, "width_percent": 6.282}, {"sql": "select * from `plugins` where `name` = '<PERSON><PERSON><PERSON>' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Tawk Chat", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.593005, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 95.353, "width_percent": 2.151}, {"sql": "select * from `plugins` where `name` = 'Facebook Messenger' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Facebook Messenger", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.593697, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 97.504, "width_percent": 2.496}]}, "models": {"data": {"App\\Models\\UserNavigation": {"value": 18, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserNavigation.php:1", "ajax": false, "filename": "UserNavigation.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ReferralProgram": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralProgram.php:1", "ajax": false, "filename": "ReferralProgram.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\ReferralLink": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralLink.php:1", "ajax": false, "filename": "ReferralLink.php", "line": "?"}}, "App\\Models\\Setting": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FSetting.php:1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\CustomCss": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCustomCss.php:1", "ajax": false, "filename": "CustomCss.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/user/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://revoaibank.test/user/referral/tree\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "254"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral", "action_name": "user.referral", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referral", "uri": "GET user/referral", "controller": "App\\Http\\Controllers\\Frontend\\ReferralController@referral<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:11\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FReferralController.php:11\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ReferralController.php:11-28</a>", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, web, web, App\\Http\\Middleware\\AllowIframeMiddleware", "duration": "480ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6InVKYnErK09Sd3llZFFxRmxnYzRqMFE9PSIsInZhbHVlIjoieTBDR01NOVU5eG5VbDljcFVBSmNaek4wY0ZyeGhweXJBUkQzcXFWcVgwZ0dGUE5XRVR2dVhnNHRTOS9TbTRUVDBkNldtSnNFUG1iWlk1Y1JVTU5FTVVRN2lWQnRjS2lzbWx4cGd4N3U0M0dMeXJyTFRRaW01OGdXNGRDQjF4K1IiLCJtYWMiOiI2OWM5Mjc5OGRmYWU3MDI3MzQ3Y2M2MjUyYjA0NGE2NDM0MmVhZWU0OTVlNWMwMjVmMWY0YWM0Y2M5OGMwZjQwIiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6IjNiMmJpMFZDRTg0VjlqV0FKcGV2d1E9PSIsInZhbHVlIjoiNmg4aE1WOUZha3VET2NVOUdMeHMvOVN0RDNkMWcwUmRrbFFhUGg3VmxmaDg3YW1WbWVUYUZWR2hiRmJ0bjB2NVU2WDdPNmhQRG4xZE8zUzMrRHBrR2g3N0J3MldwVWhYYjEyL09tNE1MY3NtVWZYNzF6SlRSVzhOTlVSQ1VXYUoiLCJtYWMiOiJmNGQ2ZDUxM2EwNzljZWY2MDIxZDI2NmM2ODA1MDJhMzM1NjI4NjAwNGQ3ZDg3MmFlMWM0NjU3NGM1ODJlYjU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">https://revoaibank.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BBpNnebL7ri3iVl8H6kWjImZ7MyO5U5HwS6wczTX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:52:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-frame-options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">ALLOWALL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"38 characters\">https://revoaibank.test/user/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/user/referral/tree</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>254</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/user/referral", "action_name": "user.referral", "controller_action": "App\\Http\\Controllers\\Frontend\\ReferralController@referral"}, "badge": null}}