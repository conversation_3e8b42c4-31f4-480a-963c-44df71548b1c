{"__meta": {"id": "01JZ4QMYA9YMPYDZ6ZG0W2JT1Z", "datetime": "2025-07-02 04:52:16", "utime": **********.329527, "method": "GET", "uri": "/admin/user?page=2&", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751431935.897032, "end": **********.329551, "duration": 0.43251895904541016, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1751431935.897032, "relative_start": 0, "end": **********.100985, "relative_end": **********.100985, "duration": 0.*****************, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.100992, "relative_start": 0.*****************, "end": **********.329553, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "229ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.106842, "relative_start": 0.****************, "end": **********.109515, "relative_end": **********.109515, "duration": 0.002672910690307617, "duration_str": "2.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.137513, "relative_start": 0.*****************, "end": **********.328194, "relative_end": **********.328194, "duration": 0.*****************, "duration_str": "191ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 25, "nb_templates": 25, "templates": [{"name": "1x backend.user.index", "param_count": null, "params": [], "start": **********.143255, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/index.blade.phpbackend.user.index", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Findex.blade.php:1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.index"}, {"name": "4x backend.filter.th", "param_count": null, "params": [], "start": **********.148207, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/filter/th.blade.phpbackend.filter.th", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Ffilter%2Fth.blade.php:1", "ajax": false, "filename": "th.blade.php", "line": "?"}, "render_count": 4, "name_original": "backend.filter.th"}, {"name": "2x backend.user.include.__avatar", "param_count": null, "params": [], "start": **********.166749, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__avatar.blade.phpbackend.user.include.__avatar", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__avatar.blade.php:1", "ajax": false, "filename": "__avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "backend.user.include.__avatar"}, {"name": "2x backend.user.include.__kyc", "param_count": null, "params": [], "start": **********.175367, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__kyc.blade.phpbackend.user.include.__kyc", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__kyc.blade.php:1", "ajax": false, "filename": "__kyc.blade.php", "line": "?"}, "render_count": 2, "name_original": "backend.user.include.__kyc"}, {"name": "2x backend.user.include.__status", "param_count": null, "params": [], "start": **********.180151, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__status.blade.phpbackend.user.include.__status", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__status.blade.php:1", "ajax": false, "filename": "__status.blade.php", "line": "?"}, "render_count": 2, "name_original": "backend.user.include.__status"}, {"name": "2x backend.user.include.__action", "param_count": null, "params": [], "start": **********.188098, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__action.blade.phpbackend.user.include.__action", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__action.blade.php:1", "ajax": false, "filename": "__action.blade.php", "line": "?"}, "render_count": 2, "name_original": "backend.user.include.__action"}, {"name": "2x backend.user.include.__delete_popup", "param_count": null, "params": [], "start": **********.193512, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__delete_popup.blade.phpbackend.user.include.__delete_popup", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__delete_popup.blade.php:1", "ajax": false, "filename": "__delete_popup.blade.php", "line": "?"}, "render_count": 2, "name_original": "backend.user.include.__delete_popup"}, {"name": "1x backend.user.include.__mail_send", "param_count": null, "params": [], "start": **********.22194, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__mail_send.blade.phpbackend.user.include.__mail_send", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__mail_send.blade.php:1", "ajax": false, "filename": "__mail_send.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__mail_send"}, {"name": "1x backend.include.__pagination", "param_count": null, "params": [], "start": **********.226705, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__pagination.blade.phpbackend.include.__pagination", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__pagination.blade.php:1", "ajax": false, "filename": "__pagination.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__pagination"}, {"name": "1x backend.layouts.app", "param_count": null, "params": [], "start": **********.231391, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.layouts.app"}, {"name": "1x backend.include.__head", "param_count": null, "params": [], "start": **********.23591, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.247066, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x backend.include.__header", "param_count": null, "params": [], "start": **********.252065, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__header"}, {"name": "1x global.__notification_data", "param_count": null, "params": [], "start": **********.281599, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_data.blade.phpglobal.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_data"}, {"name": "1x backend.include.__side_nav", "param_count": null, "params": [], "start": **********.288906, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__side_nav"}, {"name": "1x backend.include.__script", "param_count": null, "params": [], "start": **********.322902, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__script"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.327712, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET admin/user", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check, permission:customer-list|customer-login|customer-mail-send|customer-basic-manage|customer-change-password|all-type-status|customer-balance-add-or-subtract", "as": "admin.user.index", "controller": "App\\Http\\Controllers\\Backend\\UserController@index<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:57-87</a>"}, "queries": {"count": 13, "nb_statements": 13, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0074399999999999996, "accumulated_duration_str": "7.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.115654, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 9.274}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 11 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [11, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.126821, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "revoaibank", "explain": null, "start_percent": 9.274, "width_percent": 10.887}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (11) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.128356, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "revoaibank", "explain": null, "start_percent": 20.161, "width_percent": 6.317}, {"sql": "select count(*) as aggregate from `users`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1321561, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "UserController.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 82}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:82", "ajax": false, "filename": "UserController.php", "line": "82"}, "connection": "revoaibank", "explain": null, "start_percent": 26.478, "width_percent": 13.978}, {"sql": "select * from `users` order by `created_at` desc limit 15 offset 15", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1336749, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "UserController.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 82}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:82", "ajax": false, "filename": "UserController.php", "line": "82"}, "connection": "revoaibank", "explain": null, "start_percent": 40.457, "width_percent": 5.78}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 3 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [3, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 191}, {"index": 26, "namespace": "view", "name": "backend.user.index", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/index.blade.php", "line": 76}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.169702, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 46.237, "width_percent": 9.812}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 1 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [1, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 191}, {"index": 26, "namespace": "view", "name": "backend.user.index", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/index.blade.php", "line": 76}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.19878, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 56.048, "width_percent": 7.258}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2699192, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:31", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:31", "ajax": false, "filename": "__header.blade.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 63.306, "width_percent": 8.602}, {"sql": "select * from `notifications` where `for` = 'admin' order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.271373, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 71.909, "width_percent": 6.317}, {"sql": "select * from `users` where `users`.`id` in (179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.272928, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 78.226, "width_percent": 4.839}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.273728, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:41", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:41", "ajax": false, "filename": "__header.blade.php", "line": "41"}, "connection": "revoaibank", "explain": null, "start_percent": 83.065, "width_percent": 5.242}, {"sql": "select * from `notifications` where `for` = 'admin'", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2745688, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 88.306, "width_percent": 5.645}, {"sql": "select * from `users` where `users`.`id` in (1, 2, 3, 4, 11, 177, 178, 179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.2759671, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 93.952, "width_percent": 6.048}]}, "models": {"data": {"App\\Models\\Notification": {"value": 39, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 48, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 77, "messages": [{"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130945, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1728771535 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728771535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188725, "xdebug_link": null}, {"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674474121 data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674474121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188975, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-494423666 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494423666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.29044, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-497699119 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497699119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.290814, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-766067425 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766067425\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.291093, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-304515035 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-304515035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.291348, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-915815354 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915815354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.291618, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1026572882 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026572882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.294134, "xdebug_link": null}, {"message": "[\n  ability => wallet-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1143493156 data-indent-pad=\"  \"><span class=sf-dump-note>wallet-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">wallet-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143493156\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.294568, "xdebug_link": null}, {"message": "[\n  ability => virtual-card-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-310356970 data-indent-pad=\"  \"><span class=sf-dump-note>virtual-card-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">virtual-card-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310356970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.294955, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">user-paybacks-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.295496, "xdebug_link": null}, {"message": "[\n  ability => bank-profit,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>bank-profit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bank-profit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.295769, "xdebug_link": null}, {"message": "[\n  ability => pending-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>pending-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">pending-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296023, "xdebug_link": null}, {"message": "[\n  ability => rejected-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-811056594 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">rejected-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811056594\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.2963, "xdebug_link": null}, {"message": "[\n  ability => all-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1557929062 data-indent-pad=\"  \"><span class=sf-dump-note>all-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">all-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557929062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296549, "xdebug_link": null}, {"message": "[\n  ability => allied-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>allied-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">allied-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296798, "xdebug_link": null}, {"message": "[\n  ability => others-bank-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">others-bank-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297175, "xdebug_link": null}, {"message": "[\n  ability => wire-transfer,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>wire-transfer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">wire-transfer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297428, "xdebug_link": null}, {"message": "[\n  ability => others-bank-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">others-bank-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.29768, "xdebug_link": null}, {"message": "[\n  ability => dps-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>dps-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dps-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297941, "xdebug_link": null}, {"message": "[\n  ability => ongoing-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1142223902 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142223902\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298202, "xdebug_link": null}, {"message": "[\n  ability => payable-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1558850625 data-indent-pad=\"  \"><span class=sf-dump-note>payable-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">payable-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558850625\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298465, "xdebug_link": null}, {"message": "[\n  ability => complete-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-192560880 data-indent-pad=\"  \"><span class=sf-dump-note>complete-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">complete-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-192560880\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298728, "xdebug_link": null}, {"message": "[\n  ability => closed-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047397463 data-indent-pad=\"  \"><span class=sf-dump-note>closed-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047397463\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.298992, "xdebug_link": null}, {"message": "[\n  ability => all-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1129747172 data-indent-pad=\"  \"><span class=sf-dump-note>all-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129747172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299259, "xdebug_link": null}, {"message": "[\n  ability => fdr-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-482637853 data-indent-pad=\"  \"><span class=sf-dump-note>fdr-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">fdr-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-482637853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299555, "xdebug_link": null}, {"message": "[\n  ability => ongoing-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1274015817 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274015817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.299925, "xdebug_link": null}, {"message": "[\n  ability => closed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-81907353 data-indent-pad=\"  \"><span class=sf-dump-note>closed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-81907353\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30021, "xdebug_link": null}, {"message": "[\n  ability => completed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1518965161 data-indent-pad=\"  \"><span class=sf-dump-note>completed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">completed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518965161\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.300607, "xdebug_link": null}, {"message": "[\n  ability => all-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1922902561 data-indent-pad=\"  \"><span class=sf-dump-note>all-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1922902561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.300887, "xdebug_link": null}, {"message": "[\n  ability => pending-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1158548459 data-indent-pad=\"  \"><span class=sf-dump-note>pending-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">pending-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1158548459\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.301211, "xdebug_link": null}, {"message": "[\n  ability => running-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1456393887 data-indent-pad=\"  \"><span class=sf-dump-note>running-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">running-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1456393887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.301515, "xdebug_link": null}, {"message": "[\n  ability => due-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-108452275 data-indent-pad=\"  \"><span class=sf-dump-note>due-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108452275\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.301804, "xdebug_link": null}, {"message": "[\n  ability => paid-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1357764310 data-indent-pad=\"  \"><span class=sf-dump-note>paid-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">paid-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357764310\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302102, "xdebug_link": null}, {"message": "[\n  ability => rejected-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-330726700 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">rejected-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330726700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302394, "xdebug_link": null}, {"message": "[\n  ability => all-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-109446274 data-indent-pad=\"  \"><span class=sf-dump-note>all-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">all-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109446274\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302705, "xdebug_link": null}, {"message": "[\n  ability => loan-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-983732394 data-indent-pad=\"  \"><span class=sf-dump-note>loan-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">loan-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983732394\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.302989, "xdebug_link": null}, {"message": "[\n  ability => bill-service-import,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-953575014 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">bill-service-import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953575014\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303285, "xdebug_link": null}, {"message": "[\n  ability => bill-convert-rate,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-396343561 data-indent-pad=\"  \"><span class=sf-dump-note>bill-convert-rate </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-convert-rate</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-396343561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.303631, "xdebug_link": null}, {"message": "[\n  ability => bill-service-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1023322352 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-service-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023322352\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30393, "xdebug_link": null}, {"message": "[\n  ability => all-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1182622989 data-indent-pad=\"  \"><span class=sf-dump-note>all-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182622989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304237, "xdebug_link": null}, {"message": "[\n  ability => pending-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2010558432 data-indent-pad=\"  \"><span class=sf-dump-note>pending-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pending-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010558432\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304529, "xdebug_link": null}, {"message": "[\n  ability => complete-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-402745320 data-indent-pad=\"  \"><span class=sf-dump-note>complete-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">complete-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402745320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.304831, "xdebug_link": null}, {"message": "[\n  ability => return-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-869006376 data-indent-pad=\"  \"><span class=sf-dump-note>return-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">return-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869006376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305135, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1850459101 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850459101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305481, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1802547158 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802547158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.305966, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1448428246 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448428246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306328, "xdebug_link": null}, {"message": "[\n  ability => training-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-941879252 data-indent-pad=\"  \"><span class=sf-dump-note>training-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">training-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941879252\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306735, "xdebug_link": null}, {"message": "[\n  ability => training-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-543179758 data-indent-pad=\"  \"><span class=sf-dump-note>training-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">training-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-543179758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307239, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1640937029 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1640937029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307561, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1487891479 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487891479\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.307886, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-134646704 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-134646704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.30835, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1293433004 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293433004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.308723, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1811383937 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811383937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309064, "xdebug_link": null}, {"message": "[\n  ability => reward-earning-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-12463008 data-indent-pad=\"  \"><span class=sf-dump-note>reward-earning-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">reward-earning-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12463008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309397, "xdebug_link": null}, {"message": "[\n  ability => reward-redeem-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-277569416 data-indent-pad=\"  \"><span class=sf-dump-note>reward-redeem-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">reward-redeem-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277569416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.309742, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-728066895 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-728066895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310098, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1836944462 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836944462\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310474, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-934053514 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934053514\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.310847, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1902119085 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1902119085\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311185, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-183824111 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183824111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.311533, "xdebug_link": null}, {"message": "[\n  ability => sms-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-927433631 data-indent-pad=\"  \"><span class=sf-dump-note>sms-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sms-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927433631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.312043, "xdebug_link": null}, {"message": "[\n  ability => push-notification-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1064948801 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">push-notification-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064948801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.312435, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168160879 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168160879\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31286, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-162824811 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162824811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.313206, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2103637274 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2103637274\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.313585, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508902944 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508902944\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314351, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1604311401 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604311401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.315097, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1281163980 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1281163980\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.315444, "xdebug_link": null}, {"message": "[\n  ability => sms-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-450608930 data-indent-pad=\"  \"><span class=sf-dump-note>sms-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">sms-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-450608930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31581, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1932298391 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932298391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316166, "xdebug_link": null}, {"message": "[\n  ability => push-notification-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-160040042 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">push-notification-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-160040042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31655, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1058972158 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1058972158\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316939, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1585141301 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585141301\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317307, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920674415 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920674415\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317695, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31807, "xdebug_link": null}]}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/admin/user\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://revoaibank.test/user/referral/tree\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "228", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user?page=2", "action_name": "admin.user.index", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@index", "uri": "GET admin/user", "controller": "App\\Http\\Controllers\\Backend\\UserController@index<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:57-87</a>", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "duration": "433ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>page</span>\" => \"<span class=sf-dump-str>2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6ImxwSmZ4b3hmWlVqcjJIYzdJVGpiV1E9PSIsInZhbHVlIjoiSzUzdjl4VUpIenlBbjZ0dTIyOXpGWW5LaUgvQXhyRkhkMGFENGl0RzlYNVlHM1VJbGM1ZU5zSXRSc0Jja2lMblBiVU9QTUFIa0hSdSs4cFNUWFlmbzFET3VCOFZRNWRzM3REZUprOStVUHRHVDZkWHBFZ3dHSEtHMG1nT2Z3V0kiLCJtYWMiOiJkMjllZDE0MjM0NTkxNTMzNmI3OWM1YjhhZTg2YzBiODA4NjFkNDA1YzNmOGYzY2Y4ZGRkYmRmYzFkNGFjNmQ2IiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6Ii9KMCtuQUJkN0xscHF4TEJiOThJd0E9PSIsInZhbHVlIjoiN044MVFnN3RYT0hXbHF0MUw4d05seFZWRXdhSGczQXlDNko0ODJnUXdVMzFXc1VXKzQweG5pbEltNTQvOHFtSzNZMjBFMjgyQTFOOThnaEJPdW94cmNKWTZSQVZtN3BrSFM5ZXlOZFJvM2xRL1hnQyszU0RCZXdjdGJybkV5VDkiLCJtYWMiOiI1NDU5MjY5MjMwNmI4NmZkZTdmMjdjYmFjMjVkMmZiMWIzMWQ5NTkwYWEwZjU3OWZhZDY3YjMzYjgxYTYzMmNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">https://revoaibank.test/admin/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LQmddLR34V5gx8fxxD8s2W20y4FntkShwkOAEGlR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:52:16 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://revoaibank.test/admin/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/user/referral/tree</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>228</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user?page=2", "action_name": "admin.user.index", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@index"}, "badge": null}}