{"__meta": {"id": "01JZ4Q9X813ZA50XW5YCB92MZ7", "datetime": "2025-07-02 04:46:14", "utime": **********.786309, "method": "GET", "uri": "/admin/user/228/edit", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[04:46:14] LOG.warning: ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\revoaibank\\storage\\framework\\views\\53c2689336b2ebae9d0d54e44a025e7d.php on line 47", "message_html": null, "is_string": false, "label": "warning", "time": **********.595229, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.301611, "end": **********.786333, "duration": 0.****************, "duration_str": "485ms", "measures": [{"label": "Booting", "start": **********.301611, "relative_start": 0, "end": **********.502502, "relative_end": **********.502502, "duration": 0.*****************, "duration_str": "201ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.502509, "relative_start": 0.****************, "end": **********.786335, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "284ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.50833, "relative_start": 0.*****************, "end": **********.511064, "relative_end": **********.511064, "duration": 0.002733945846557617, "duration_str": "2.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.583414, "relative_start": 0.****************, "end": **********.785001, "relative_end": **********.785001, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x backend.user.edit", "param_count": null, "params": [], "start": **********.588993, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.phpbackend.user.edit", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.edit"}, {"name": "1x backend.user.include.__delete_popup", "param_count": null, "params": [], "start": **********.603787, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__delete_popup.blade.phpbackend.user.include.__delete_popup", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__delete_popup.blade.php:1", "ajax": false, "filename": "__delete_popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__delete_popup"}, {"name": "1x backend.user.include.__status_update", "param_count": null, "params": [], "start": **********.612332, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__status_update.blade.phpbackend.user.include.__status_update", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__status_update.blade.php:1", "ajax": false, "filename": "__status_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__status_update"}, {"name": "1x backend.user.include.__basic_info", "param_count": null, "params": [], "start": **********.622792, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__basic_info.blade.phpbackend.user.include.__basic_info", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__basic_info.blade.php:1", "ajax": false, "filename": "__basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__basic_info"}, {"name": "1x backend.user.include.__earnings", "param_count": null, "params": [], "start": **********.63243, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__earnings.blade.phpbackend.user.include.__earnings", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__earnings.blade.php:1", "ajax": false, "filename": "__earnings.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__earnings"}, {"name": "1x backend.user.include.__dps", "param_count": null, "params": [], "start": **********.63737, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__dps.blade.phpbackend.user.include.__dps", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__dps.blade.php:1", "ajax": false, "filename": "__dps.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__dps"}, {"name": "1x backend.user.include.__fdr", "param_count": null, "params": [], "start": **********.64228, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__fdr.blade.phpbackend.user.include.__fdr", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__fdr.blade.php:1", "ajax": false, "filename": "__fdr.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__fdr"}, {"name": "1x backend.user.include.__loan", "param_count": null, "params": [], "start": **********.647471, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__loan.blade.phpbackend.user.include.__loan", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__loan.blade.php:1", "ajax": false, "filename": "__loan.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__loan"}, {"name": "1x backend.user.include.__card", "param_count": null, "params": [], "start": **********.654644, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__card.blade.phpbackend.user.include.__card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__card.blade.php:1", "ajax": false, "filename": "__card.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__card"}, {"name": "1x backend.user.include.__transactions", "param_count": null, "params": [], "start": **********.659666, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__transactions.blade.phpbackend.user.include.__transactions", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__transactions.blade.php:1", "ajax": false, "filename": "__transactions.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__transactions"}, {"name": "1x backend.user.include.__referral_tree", "param_count": null, "params": [], "start": **********.664696, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__referral_tree.blade.phpbackend.user.include.__referral_tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__referral_tree.blade.php:1", "ajax": false, "filename": "__referral_tree.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__referral_tree"}, {"name": "1x backend.user.include.__ticket", "param_count": null, "params": [], "start": **********.669491, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__ticket.blade.phpbackend.user.include.__ticket", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__ticket.blade.php:1", "ajax": false, "filename": "__ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__ticket"}, {"name": "1x backend.user.include.__mail_send", "param_count": null, "params": [], "start": **********.674325, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__mail_send.blade.phpbackend.user.include.__mail_send", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__mail_send.blade.php:1", "ajax": false, "filename": "__mail_send.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__mail_send"}, {"name": "1x backend.user.include.__balance", "param_count": null, "params": [], "start": **********.679251, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__balance.blade.phpbackend.user.include.__balance", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__balance.blade.php:1", "ajax": false, "filename": "__balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__balance"}, {"name": "1x backend.layouts.app", "param_count": null, "params": [], "start": **********.690799, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.layouts.app"}, {"name": "1x backend.include.__head", "param_count": null, "params": [], "start": **********.695697, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.706689, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x backend.include.__header", "param_count": null, "params": [], "start": **********.711818, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__header"}, {"name": "1x global.__notification_data", "param_count": null, "params": [], "start": **********.740117, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_data.blade.phpglobal.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_data"}, {"name": "1x backend.include.__side_nav", "param_count": null, "params": [], "start": **********.747294, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__side_nav"}, {"name": "1x backend.include.__script", "param_count": null, "params": [], "start": **********.779875, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__script"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.784531, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET admin/user/{user}/edit", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check, permission:customer-basic-manage|customer-change-password|all-type-status|customer-balance-add-or-subtract", "as": "admin.user.edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.*****************, "accumulated_duration_str": "30ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.5172539, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 2.367}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 11 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [11, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.528514, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "revoaibank", "explain": null, "start_percent": 2.367, "width_percent": 2.333}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (11) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.52993, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "revoaibank", "explain": null, "start_percent": 4.7, "width_percent": 1.533}, {"sql": "select * from `users` where `users`.`id` = '228' limit 1", "type": "query", "params": [], "bindings": ["228"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.533585, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "UserController.php:201", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:201", "ajax": false, "filename": "UserController.php", "line": "201"}, "connection": "revoaibank", "explain": null, "start_percent": 6.233, "width_percent": 1.767}, {"sql": "select max(`the_order`) as aggregate from `level_referrals` where `type` = 'investment'", "type": "query", "params": [], "bindings": ["investment"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.534869, "duration": 0.01158, "duration_str": "11.58ms", "memory": 0, "memory_str": null, "filename": "UserController.php:202", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:202", "ajax": false, "filename": "UserController.php", "line": "202"}, "connection": "revoaibank", "explain": null, "start_percent": 8, "width_percent": 38.6}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 228 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [228, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5500722, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "UserController.php:214", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:214", "ajax": false, "filename": "UserController.php", "line": "214"}, "connection": "revoaibank", "explain": null, "start_percent": 46.6, "width_percent": 6.833}, {"sql": "select `id` from `currencies` where `is_default` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.552773, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "User.php:401", "source": {"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:401", "ajax": false, "filename": "User.php", "line": "401"}, "connection": "revoaibank", "explain": null, "start_percent": 53.433, "width_percent": 0.767}, {"sql": "insert into `user_wallets` (`balance`, `currency_id`, `user_id`, `updated_at`, `created_at`) values (0, 36, 228, '2025-07-02 04:46:14', '2025-07-02 04:46:14')", "type": "query", "params": [], "bindings": [0, 36, 228, "2025-07-02 04:46:14", "2025-07-02 04:46:14"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.553607, "duration": 0.00561, "duration_str": "5.61ms", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "revoaibank", "explain": null, "start_percent": 54.2, "width_percent": 18.7}, {"sql": "select * from `branches` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5603478, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "UserController.php:333", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:333", "ajax": false, "filename": "UserController.php", "line": "333"}, "connection": "revoaibank", "explain": null, "start_percent": 72.9, "width_percent": 2}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'deposit' or `type` = 'manual_deposit')", "type": "query", "params": [], "bindings": [228, "success", "deposit", "manual_deposit"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 196}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 336}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.5632792, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "User.php:259", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:259", "ajax": false, "filename": "User.php", "line": "259"}, "connection": "revoaibank", "explain": null, "start_percent": 74.9, "width_percent": 1.567}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'fund_transfer')", "type": "query", "params": [], "bindings": [228, "success", "fund_transfer"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 337}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.564177, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:289", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:289", "ajax": false, "filename": "User.php", "line": "289"}, "connection": "revoaibank", "explain": null, "start_percent": 76.467, "width_percent": 0.967}, {"sql": "select * from `dps` where `dps`.`user_id` = 228 and `dps`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.5651438, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "UserController.php:338", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:338", "ajax": false, "filename": "UserController.php", "line": "338"}, "connection": "revoaibank", "explain": null, "start_percent": 77.433, "width_percent": 1.333}, {"sql": "select * from `fdr` where `fdr`.`user_id` = 228 and `fdr`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.566176, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "UserController.php:339", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:339", "ajax": false, "filename": "UserController.php", "line": "339"}, "connection": "revoaibank", "explain": null, "start_percent": 78.767, "width_percent": 1.4}, {"sql": "select * from `loans` where `loans`.`user_id` = 228 and `loans`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.567246, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "UserController.php:340", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:340", "ajax": false, "filename": "UserController.php", "line": "340"}, "connection": "revoaibank", "explain": null, "start_percent": 80.167, "width_percent": 1.3}, {"sql": "select * from `bills` where `bills`.`user_id` = 228 and `bills`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.568251, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "UserController.php:341", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:341", "ajax": false, "filename": "UserController.php", "line": "341"}, "connection": "revoaibank", "explain": null, "start_percent": 81.467, "width_percent": 1.8}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'withdraw' or `type` = 'withdraw_auto')", "type": "query", "params": [], "bindings": [228, "success", "withdraw", "withdraw_auto"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.569322, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "User.php:280", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:280", "ajax": false, "filename": "User.php", "line": "280"}, "connection": "revoaibank", "explain": null, "start_percent": 83.267, "width_percent": 1.1}, {"sql": "select * from `tickets` where `tickets`.`user_id` = 228 and `tickets`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.570775, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "UserController.php:343", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:343", "ajax": false, "filename": "UserController.php", "line": "343"}, "connection": "revoaibank", "explain": null, "start_percent": 84.367, "width_percent": 1.3}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [228, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 345}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.571655, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 85.667, "width_percent": 1.067}, {"sql": "select count(*) as aggregate from `users` where `users`.`ref_id` = 228 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.572361, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "UserController.php:346", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:346", "ajax": false, "filename": "UserController.php", "line": "346"}, "connection": "revoaibank", "explain": null, "start_percent": 86.733, "width_percent": 0.9}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 228 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.581156, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 87.633, "width_percent": 1.033}, {"sql": "select * from `currencies` where `currencies`.`id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5819368, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 88.667, "width_percent": 0.733}, {"sql": "select * from `login_activities` where `login_activities`.`user_id` = 228 and `login_activities`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5957499, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "backend.user.edit:50", "source": {"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:50", "ajax": false, "filename": "edit.blade.php", "line": "50"}, "connection": "revoaibank", "explain": null, "start_percent": 89.4, "width_percent": 2.6}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7297711, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:31", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:31", "ajax": false, "filename": "__header.blade.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 92, "width_percent": 2.133}, {"sql": "select * from `notifications` where `for` = 'admin' order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7312372, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 94.133, "width_percent": 1.567}, {"sql": "select * from `users` where `users`.`id` in (179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.732319, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 95.7, "width_percent": 1.133}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.733124, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:41", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:41", "ajax": false, "filename": "__header.blade.php", "line": "41"}, "connection": "revoaibank", "explain": null, "start_percent": 96.833, "width_percent": 0.9}, {"sql": "select * from `notifications` where `for` = 'admin'", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.73381, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 97.733, "width_percent": 1.133}, {"sql": "select * from `users` where `users`.`id` in (1, 2, 3, 4, 11, 177, 178, 179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.734871, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 98.867, "width_percent": 1.133}]}, "models": {"data": {"App\\Models\\Notification": {"value": 39, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 50, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 89, "messages": [{"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.532472, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1878600966 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1878600966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597408, "xdebug_link": null}, {"message": "[\n  ability => customer-login,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-354052155 data-indent-pad=\"  \"><span class=sf-dump-note>customer-login </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">customer-login</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354052155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.597638, "xdebug_link": null}, {"message": "[\n  ability => customer-balance-add-or-subtract,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1236659464 data-indent-pad=\"  \"><span class=sf-dump-note>customer-balance-add-or-subtract </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customer-balance-add-or-subtract</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1236659464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.598231, "xdebug_link": null}, {"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1246244375 data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246244375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.59844, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-806614967 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806614967\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.606856, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1448594034 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1448594034\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607291, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1186248859 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">subscribe-user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186248859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607689, "xdebug_link": null}, {"message": "[\n  ability => all-type-status,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-179905435 data-indent-pad=\"  \"><span class=sf-dump-note>all-type-status </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">all-type-status</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179905435\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.607931, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-107320645 data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user-paybacks</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-107320645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613586, "xdebug_link": null}, {"message": "[\n  ability => user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2002467405 data-indent-pad=\"  \"><span class=sf-dump-note>user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002467405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614063, "xdebug_link": null}, {"message": "[\n  ability => user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-318632145 data-indent-pad=\"  \"><span class=sf-dump-note>user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318632145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614471, "xdebug_link": null}, {"message": "[\n  ability => user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-659459123 data-indent-pad=\"  \"><span class=sf-dump-note>user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659459123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.614958, "xdebug_link": null}, {"message": "[\n  ability => user-cards,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-472409805 data-indent-pad=\"  \"><span class=sf-dump-note>user-cards </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user-cards</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-472409805\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617595, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1240290218 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240290218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.617869, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2065092751 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065092751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.618271, "xdebug_link": null}, {"message": "[\n  ability => customer-change-password,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-229748623 data-indent-pad=\"  \"><span class=sf-dump-note>customer-change-password </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">customer-change-password</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229748623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.627881, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1608243812 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608243812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748661, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1385205847 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385205847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749035, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2040473487 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040473487\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749315, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-426400237 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426400237\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749542, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1443173800 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443173800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749789, "xdebug_link": null}, {"message": "[\n  ability => wallet-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1160608149 data-indent-pad=\"  \"><span class=sf-dump-note>wallet-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">wallet-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160608149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752424, "xdebug_link": null}, {"message": "[\n  ability => virtual-card-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-617363359 data-indent-pad=\"  \"><span class=sf-dump-note>virtual-card-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">virtual-card-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-617363359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.752811, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">user-paybacks-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753243, "xdebug_link": null}, {"message": "[\n  ability => bank-profit,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>bank-profit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bank-profit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753501, "xdebug_link": null}, {"message": "[\n  ability => pending-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>pending-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">pending-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.753749, "xdebug_link": null}, {"message": "[\n  ability => rejected-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-191495284 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">rejected-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-191495284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754032, "xdebug_link": null}, {"message": "[\n  ability => all-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1702547809 data-indent-pad=\"  \"><span class=sf-dump-note>all-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">all-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1702547809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754281, "xdebug_link": null}, {"message": "[\n  ability => allied-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>allied-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">allied-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754543, "xdebug_link": null}, {"message": "[\n  ability => others-bank-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">others-bank-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.754976, "xdebug_link": null}, {"message": "[\n  ability => wire-transfer,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>wire-transfer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">wire-transfer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.755241, "xdebug_link": null}, {"message": "[\n  ability => others-bank-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">others-bank-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75549, "xdebug_link": null}, {"message": "[\n  ability => dps-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>dps-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dps-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.755747, "xdebug_link": null}, {"message": "[\n  ability => ongoing-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-604429889 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604429889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756018, "xdebug_link": null}, {"message": "[\n  ability => payable-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382968996 data-indent-pad=\"  \"><span class=sf-dump-note>payable-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">payable-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382968996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756282, "xdebug_link": null}, {"message": "[\n  ability => complete-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805587757 data-indent-pad=\"  \"><span class=sf-dump-note>complete-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">complete-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805587757\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756543, "xdebug_link": null}, {"message": "[\n  ability => closed-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-84419139 data-indent-pad=\"  \"><span class=sf-dump-note>closed-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-84419139\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.756805, "xdebug_link": null}, {"message": "[\n  ability => all-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1133958400 data-indent-pad=\"  \"><span class=sf-dump-note>all-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1133958400\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757082, "xdebug_link": null}, {"message": "[\n  ability => fdr-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1356223445 data-indent-pad=\"  \"><span class=sf-dump-note>fdr-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">fdr-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356223445\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757374, "xdebug_link": null}, {"message": "[\n  ability => ongoing-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-80447501 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80447501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757734, "xdebug_link": null}, {"message": "[\n  ability => closed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-777035214 data-indent-pad=\"  \"><span class=sf-dump-note>closed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777035214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.758047, "xdebug_link": null}, {"message": "[\n  ability => completed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-832466553 data-indent-pad=\"  \"><span class=sf-dump-note>completed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">completed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832466553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.758414, "xdebug_link": null}, {"message": "[\n  ability => all-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-377064904 data-indent-pad=\"  \"><span class=sf-dump-note>all-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-377064904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75869, "xdebug_link": null}, {"message": "[\n  ability => pending-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-190228193 data-indent-pad=\"  \"><span class=sf-dump-note>pending-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">pending-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-190228193\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759003, "xdebug_link": null}, {"message": "[\n  ability => running-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-16979284 data-indent-pad=\"  \"><span class=sf-dump-note>running-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">running-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16979284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759304, "xdebug_link": null}, {"message": "[\n  ability => due-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-820248080 data-indent-pad=\"  \"><span class=sf-dump-note>due-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820248080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75959, "xdebug_link": null}, {"message": "[\n  ability => paid-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-715512036 data-indent-pad=\"  \"><span class=sf-dump-note>paid-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">paid-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-715512036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.759875, "xdebug_link": null}, {"message": "[\n  ability => rejected-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-387715474 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">rejected-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387715474\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760162, "xdebug_link": null}, {"message": "[\n  ability => all-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382616639 data-indent-pad=\"  \"><span class=sf-dump-note>all-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">all-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382616639\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760449, "xdebug_link": null}, {"message": "[\n  ability => loan-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1390661434 data-indent-pad=\"  \"><span class=sf-dump-note>loan-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">loan-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390661434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760726, "xdebug_link": null}, {"message": "[\n  ability => bill-service-import,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-384584907 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">bill-service-import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384584907\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76102, "xdebug_link": null}, {"message": "[\n  ability => bill-convert-rate,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-917409086 data-indent-pad=\"  \"><span class=sf-dump-note>bill-convert-rate </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-convert-rate</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-917409086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761342, "xdebug_link": null}, {"message": "[\n  ability => bill-service-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-775906305 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-service-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-775906305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761636, "xdebug_link": null}, {"message": "[\n  ability => all-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-822403748 data-indent-pad=\"  \"><span class=sf-dump-note>all-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-822403748\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761934, "xdebug_link": null}, {"message": "[\n  ability => pending-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1083566099 data-indent-pad=\"  \"><span class=sf-dump-note>pending-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pending-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083566099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.762234, "xdebug_link": null}, {"message": "[\n  ability => complete-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-997867206 data-indent-pad=\"  \"><span class=sf-dump-note>complete-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">complete-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-997867206\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.762533, "xdebug_link": null}, {"message": "[\n  ability => return-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-402816208 data-indent-pad=\"  \"><span class=sf-dump-note>return-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">return-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-402816208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.762834, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-636889248 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636889248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.763199, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1129030209 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1129030209\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.763603, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1891995593 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891995593\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.763955, "xdebug_link": null}, {"message": "[\n  ability => training-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1415272575 data-indent-pad=\"  \"><span class=sf-dump-note>training-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">training-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1415272575\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.764356, "xdebug_link": null}, {"message": "[\n  ability => training-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1814146342 data-indent-pad=\"  \"><span class=sf-dump-note>training-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">training-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1814146342\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.764829, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1893655362 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893655362\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.765153, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2015586692 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015586692\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76545, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1406071183 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406071183\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.765859, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1477611741 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477611741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.7662, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1546875865 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546875865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.766519, "xdebug_link": null}, {"message": "[\n  ability => reward-earning-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-384971706 data-indent-pad=\"  \"><span class=sf-dump-note>reward-earning-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">reward-earning-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384971706\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76684, "xdebug_link": null}, {"message": "[\n  ability => reward-redeem-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-659161556 data-indent-pad=\"  \"><span class=sf-dump-note>reward-redeem-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">reward-redeem-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-659161556\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.767187, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-605367749 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-605367749\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.76753, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-277822743 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277822743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.767898, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-777509795 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777509795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768264, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-306976216 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306976216\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768596, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-940913348 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-940913348\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768942, "xdebug_link": null}, {"message": "[\n  ability => sms-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2087190321 data-indent-pad=\"  \"><span class=sf-dump-note>sms-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sms-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2087190321\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769404, "xdebug_link": null}, {"message": "[\n  ability => push-notification-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1576173508 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">push-notification-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576173508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.769779, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1895556693 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1895556693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770168, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-20215055 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20215055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770505, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993036258 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993036258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.770875, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-754917452 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-754917452\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771702, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-515882601 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515882601\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.772421, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-585364002 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585364002\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.772765, "xdebug_link": null}, {"message": "[\n  ability => sms-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-28038292 data-indent-pad=\"  \"><span class=sf-dump-note>sms-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">sms-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28038292\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773132, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1573943872 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573943872\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773496, "xdebug_link": null}, {"message": "[\n  ability => push-notification-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-219179327 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">push-notification-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-219179327\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773874, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1488092534 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1488092534\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774298, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1353573112 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1353573112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.77473, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.775127, "xdebug_link": null}]}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/admin/user\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/228/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit", "uri": "GET admin/user/{user}/edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "duration": "486ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6IlVwbXQ5Q0FveWI2eEhqWElPVDIrb3c9PSIsInZhbHVlIjoibUxLMytGS0JTV0Joc2ZtNlZNczRpeXA4QkpVYmp1UEY4TUVxOGgrQzgrNk5LaTVoOEdjUVdMcHdWMXdoVUQ4L2g1OVlqZHNzQk1ONkRXUXpwdFN3R1QrOU81aHc4VHpaRHQxR1Y5Y3dZT0lVcm91NVF1dTRrdWN3UGlmcGJJVlQiLCJtYWMiOiI4ZjBmMzQ1MjgzNWQxZjVjN2FhODE0ZDc2OTk0Y2NkNjc0ZTAzYjdkNmMyNTJlNGQzYmRkZTI0Mjg4ZjhkODUyIiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6Ik9NS1c0d3NFaG5RK0xvRjVKZnE2aGc9PSIsInZhbHVlIjoiSlhYb05naWlaZjZMTno1TnRML2QwR0xjQUtiNm9YemRPT1NhQ2t4MmRVeHFUNWIzUXJDRHQzWGhFb0lEK0dCaHhoZFphZ0JGSk5xVHplN0IzbHVybG92cU10QnpWdWVCZEVHNnY4M1VzbHYybXozd3IzYjdnWlRrc2syTFJJTzEiLCJtYWMiOiJjMjYzYzc2ZTVkODJlNzdlMmNhMmU0Yzc3MmRhNDVkYWFjZWNhMDM3MTBjNjYwODkxZGViNWIxNDE1NDg0MTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">https://revoaibank.test/admin/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">nTjdSKm1091ALBPMcwS7I2uXf6FsfAGKoVqgzW93</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:46:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://revoaibank.test/admin/user</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/228/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit"}, "badge": null}}