{"__meta": {"id": "01JZ4QVPTSB4SCAYKXVSQZ7WDF", "datetime": "2025-07-02 04:55:58", "utime": **********.041981, "method": "GET", "uri": "/admin/user/267/edit", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[04:55:57] LOG.warning: ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\revoaibank\\storage\\framework\\views\\53c2689336b2ebae9d0d54e44a025e7d.php on line 47", "message_html": null, "is_string": false, "label": "warning", "time": **********.849569, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.538949, "end": **********.042029, "duration": 0.***************, "duration_str": "503ms", "measures": [{"label": "Booting", "start": **********.538949, "relative_start": 0, "end": **********.756429, "relative_end": **********.756429, "duration": 0.*****************, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.756436, "relative_start": 0.*****************, "end": **********.042032, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.762401, "relative_start": 0.*****************, "end": **********.765119, "relative_end": **********.765119, "duration": 0.0027179718017578125, "duration_str": "2.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.838682, "relative_start": 0.*****************, "end": **********.040411, "relative_end": **********.040411, "duration": 0.*****************, "duration_str": "202ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x backend.user.edit", "param_count": null, "params": [], "start": **********.844367, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.phpbackend.user.edit", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.edit"}, {"name": "1x backend.user.include.__delete_popup", "param_count": null, "params": [], "start": **********.857567, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__delete_popup.blade.phpbackend.user.include.__delete_popup", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__delete_popup.blade.php:1", "ajax": false, "filename": "__delete_popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__delete_popup"}, {"name": "1x backend.user.include.__status_update", "param_count": null, "params": [], "start": **********.866195, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__status_update.blade.phpbackend.user.include.__status_update", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__status_update.blade.php:1", "ajax": false, "filename": "__status_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__status_update"}, {"name": "1x backend.user.include.__basic_info", "param_count": null, "params": [], "start": **********.876771, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__basic_info.blade.phpbackend.user.include.__basic_info", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__basic_info.blade.php:1", "ajax": false, "filename": "__basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__basic_info"}, {"name": "1x backend.user.include.__earnings", "param_count": null, "params": [], "start": **********.886549, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__earnings.blade.phpbackend.user.include.__earnings", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__earnings.blade.php:1", "ajax": false, "filename": "__earnings.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__earnings"}, {"name": "1x backend.user.include.__dps", "param_count": null, "params": [], "start": **********.891506, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__dps.blade.phpbackend.user.include.__dps", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__dps.blade.php:1", "ajax": false, "filename": "__dps.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__dps"}, {"name": "1x backend.user.include.__fdr", "param_count": null, "params": [], "start": **********.896473, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__fdr.blade.phpbackend.user.include.__fdr", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__fdr.blade.php:1", "ajax": false, "filename": "__fdr.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__fdr"}, {"name": "1x backend.user.include.__loan", "param_count": null, "params": [], "start": **********.901824, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__loan.blade.phpbackend.user.include.__loan", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__loan.blade.php:1", "ajax": false, "filename": "__loan.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__loan"}, {"name": "1x backend.user.include.__card", "param_count": null, "params": [], "start": **********.908811, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__card.blade.phpbackend.user.include.__card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__card.blade.php:1", "ajax": false, "filename": "__card.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__card"}, {"name": "1x backend.user.include.__transactions", "param_count": null, "params": [], "start": **********.913714, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__transactions.blade.phpbackend.user.include.__transactions", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__transactions.blade.php:1", "ajax": false, "filename": "__transactions.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__transactions"}, {"name": "1x backend.user.include.__referral_tree", "param_count": null, "params": [], "start": **********.918549, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__referral_tree.blade.phpbackend.user.include.__referral_tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__referral_tree.blade.php:1", "ajax": false, "filename": "__referral_tree.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__referral_tree"}, {"name": "1x backend.user.include.__ticket", "param_count": null, "params": [], "start": **********.923279, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__ticket.blade.phpbackend.user.include.__ticket", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__ticket.blade.php:1", "ajax": false, "filename": "__ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__ticket"}, {"name": "1x backend.user.include.__mail_send", "param_count": null, "params": [], "start": **********.928043, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__mail_send.blade.phpbackend.user.include.__mail_send", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__mail_send.blade.php:1", "ajax": false, "filename": "__mail_send.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__mail_send"}, {"name": "1x backend.user.include.__balance", "param_count": null, "params": [], "start": **********.932888, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__balance.blade.phpbackend.user.include.__balance", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__balance.blade.php:1", "ajax": false, "filename": "__balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__balance"}, {"name": "1x backend.layouts.app", "param_count": null, "params": [], "start": **********.944626, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.layouts.app"}, {"name": "1x backend.include.__head", "param_count": null, "params": [], "start": **********.949316, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.960546, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x backend.include.__header", "param_count": null, "params": [], "start": **********.965711, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__header"}, {"name": "1x global.__notification_data", "param_count": null, "params": [], "start": **********.994295, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_data.blade.phpglobal.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_data"}, {"name": "1x backend.include.__side_nav", "param_count": null, "params": [], "start": **********.001633, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__side_nav"}, {"name": "1x backend.include.__script", "param_count": null, "params": [], "start": **********.035175, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__script"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.039871, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET admin/user/{user}/edit", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check, permission:customer-basic-manage|customer-change-password|all-type-status|customer-balance-add-or-subtract", "as": "admin.user.edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01553, "accumulated_duration_str": "15.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.7711802, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 4.185}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 11 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [11, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7824938, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "revoaibank", "explain": null, "start_percent": 4.185, "width_percent": 4.829}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (11) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.783936, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "revoaibank", "explain": null, "start_percent": 9.015, "width_percent": 2.704}, {"sql": "select * from `users` where `users`.`id` = '267' limit 1", "type": "query", "params": [], "bindings": ["267"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.787574, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "UserController.php:201", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:201", "ajax": false, "filename": "UserController.php", "line": "201"}, "connection": "revoaibank", "explain": null, "start_percent": 11.719, "width_percent": 3.091}, {"sql": "select max(`the_order`) as aggregate from `level_referrals` where `type` = 'investment'", "type": "query", "params": [], "bindings": ["investment"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.788705, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "UserController.php:202", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:202", "ajax": false, "filename": "UserController.php", "line": "202"}, "connection": "revoaibank", "explain": null, "start_percent": 14.81, "width_percent": 2.189}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 267 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [267, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.807118, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "UserController.php:214", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:214", "ajax": false, "filename": "UserController.php", "line": "214"}, "connection": "revoaibank", "explain": null, "start_percent": 16.999, "width_percent": 4.314}, {"sql": "select `id` from `currencies` where `is_default` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.808507, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "User.php:401", "source": {"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:401", "ajax": false, "filename": "User.php", "line": "401"}, "connection": "revoaibank", "explain": null, "start_percent": 21.314, "width_percent": 1.481}, {"sql": "insert into `user_wallets` (`balance`, `currency_id`, `user_id`, `updated_at`, `created_at`) values (0, 36, 267, '2025-07-02 04:55:57', '2025-07-02 04:55:57')", "type": "query", "params": [], "bindings": [0, 36, 267, "2025-07-02 04:55:57", "2025-07-02 04:55:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.80935, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "revoaibank", "explain": null, "start_percent": 22.795, "width_percent": 27.946}, {"sql": "select * from `branches` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.814636, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserController.php:333", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:333", "ajax": false, "filename": "UserController.php", "line": "333"}, "connection": "revoaibank", "explain": null, "start_percent": 50.741, "width_percent": 2.318}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 267 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'deposit' or `type` = 'manual_deposit')", "type": "query", "params": [], "bindings": [267, "success", "deposit", "manual_deposit"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 196}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 336}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8173091, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "User.php:259", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:259", "ajax": false, "filename": "User.php", "line": "259"}, "connection": "revoaibank", "explain": null, "start_percent": 53.059, "width_percent": 2.833}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 267 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'fund_transfer')", "type": "query", "params": [], "bindings": [267, "success", "fund_transfer"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 337}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.818136, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:289", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:289", "ajax": false, "filename": "User.php", "line": "289"}, "connection": "revoaibank", "explain": null, "start_percent": 55.892, "width_percent": 1.867}, {"sql": "select * from `dps` where `dps`.`user_id` = 267 and `dps`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.819203, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "UserController.php:338", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:338", "ajax": false, "filename": "UserController.php", "line": "338"}, "connection": "revoaibank", "explain": null, "start_percent": 57.759, "width_percent": 2.447}, {"sql": "select * from `fdr` where `fdr`.`user_id` = 267 and `fdr`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8202682, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "UserController.php:339", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:339", "ajax": false, "filename": "UserController.php", "line": "339"}, "connection": "revoaibank", "explain": null, "start_percent": 60.206, "width_percent": 4.121}, {"sql": "select * from `loans` where `loans`.`user_id` = 267 and `loans`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.82157, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "UserController.php:340", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:340", "ajax": false, "filename": "UserController.php", "line": "340"}, "connection": "revoaibank", "explain": null, "start_percent": 64.327, "width_percent": 2.382}, {"sql": "select * from `bills` where `bills`.`user_id` = 267 and `bills`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.822574, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "UserController.php:341", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:341", "ajax": false, "filename": "UserController.php", "line": "341"}, "connection": "revoaibank", "explain": null, "start_percent": 66.71, "width_percent": 2.318}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 267 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'withdraw' or `type` = 'withdraw_auto')", "type": "query", "params": [], "bindings": [267, "success", "withdraw", "withdraw_auto"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.823369, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "User.php:280", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:280", "ajax": false, "filename": "User.php", "line": "280"}, "connection": "revoaibank", "explain": null, "start_percent": 69.028, "width_percent": 1.932}, {"sql": "select * from `tickets` where `tickets`.`user_id` = 267 and `tickets`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.824911, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "UserController.php:343", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:343", "ajax": false, "filename": "UserController.php", "line": "343"}, "connection": "revoaibank", "explain": null, "start_percent": 70.959, "width_percent": 2.833}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 267 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [267, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 345}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.825909, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 73.793, "width_percent": 2.64}, {"sql": "select count(*) as aggregate from `users` where `users`.`ref_id` = 267 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8267438, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserController.php:346", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:346", "ajax": false, "filename": "UserController.php", "line": "346"}, "connection": "revoaibank", "explain": null, "start_percent": 76.433, "width_percent": 1.61}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 267 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8363, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 78.042, "width_percent": 2.382}, {"sql": "select * from `currencies` where `currencies`.`id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.837194, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 80.425, "width_percent": 1.481}, {"sql": "select * from `login_activities` where `login_activities`.`user_id` = 267 and `login_activities`.`user_id` is not null", "type": "query", "params": [], "bindings": [267], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.850037, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "backend.user.edit:50", "source": {"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:50", "ajax": false, "filename": "edit.blade.php", "line": "50"}, "connection": "revoaibank", "explain": null, "start_percent": 81.906, "width_percent": 3.284}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.983852, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:31", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:31", "ajax": false, "filename": "__header.blade.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 85.19, "width_percent": 3.928}, {"sql": "select * from `notifications` where `for` = 'admin' order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9853022, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 89.118, "width_percent": 2.898}, {"sql": "select * from `users` where `users`.`id` in (179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9863892, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 92.015, "width_percent": 2.061}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9871528, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:41", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:41", "ajax": false, "filename": "__header.blade.php", "line": "41"}, "connection": "revoaibank", "explain": null, "start_percent": 94.076, "width_percent": 1.674}, {"sql": "select * from `notifications` where `for` = 'admin'", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9878051, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 95.75, "width_percent": 2.061}, {"sql": "select * from `users` where `users`.`id` in (1, 2, 3, 4, 11, 177, 178, 179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9888551, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 97.811, "width_percent": 2.189}]}, "models": {"data": {"App\\Models\\Notification": {"value": 39, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 50, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 89, "messages": [{"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.786414, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2100816066 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2100816066\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.851399, "xdebug_link": null}, {"message": "[\n  ability => customer-login,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1566226867 data-indent-pad=\"  \"><span class=sf-dump-note>customer-login </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">customer-login</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566226867\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.851617, "xdebug_link": null}, {"message": "[\n  ability => customer-balance-add-or-subtract,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-497607035 data-indent-pad=\"  \"><span class=sf-dump-note>customer-balance-add-or-subtract </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customer-balance-add-or-subtract</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-497607035\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852188, "xdebug_link": null}, {"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-201862536 data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201862536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.852422, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-135622291 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135622291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.860611, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-783129553 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783129553\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861049, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1697531939 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">subscribe-user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697531939\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861458, "xdebug_link": null}, {"message": "[\n  ability => all-type-status,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-96761112 data-indent-pad=\"  \"><span class=sf-dump-note>all-type-status </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">all-type-status</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96761112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.861709, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1639520935 data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user-paybacks</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639520935\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867513, "xdebug_link": null}, {"message": "[\n  ability => user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1779385428 data-indent-pad=\"  \"><span class=sf-dump-note>user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779385428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.867952, "xdebug_link": null}, {"message": "[\n  ability => user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-695685333 data-indent-pad=\"  \"><span class=sf-dump-note>user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695685333\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868368, "xdebug_link": null}, {"message": "[\n  ability => user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-441352520 data-indent-pad=\"  \"><span class=sf-dump-note>user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441352520\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.868783, "xdebug_link": null}, {"message": "[\n  ability => user-cards,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1833843875 data-indent-pad=\"  \"><span class=sf-dump-note>user-cards </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user-cards</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833843875\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871408, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-311174008 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311174008\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.871684, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1821614986 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821614986\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.872109, "xdebug_link": null}, {"message": "[\n  ability => customer-change-password,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1687886299 data-indent-pad=\"  \"><span class=sf-dump-note>customer-change-password </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">customer-change-password</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1687886299\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.88161, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1767926740 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767926740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003148, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-742626027 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-742626027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003579, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1665357264 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665357264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003869, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1399676773 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399676773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.004098, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1325820833 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325820833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.004347, "xdebug_link": null}, {"message": "[\n  ability => wallet-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-49369620 data-indent-pad=\"  \"><span class=sf-dump-note>wallet-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">wallet-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-49369620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.007036, "xdebug_link": null}, {"message": "[\n  ability => virtual-card-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1790966688 data-indent-pad=\"  \"><span class=sf-dump-note>virtual-card-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">virtual-card-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790966688\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.007432, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">user-paybacks-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008015, "xdebug_link": null}, {"message": "[\n  ability => bank-profit,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>bank-profit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bank-profit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008291, "xdebug_link": null}, {"message": "[\n  ability => pending-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>pending-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">pending-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008544, "xdebug_link": null}, {"message": "[\n  ability => rejected-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-334361162 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">rejected-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334361162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008815, "xdebug_link": null}, {"message": "[\n  ability => all-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1373934285 data-indent-pad=\"  \"><span class=sf-dump-note>all-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">all-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373934285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009071, "xdebug_link": null}, {"message": "[\n  ability => allied-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>allied-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">allied-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009327, "xdebug_link": null}, {"message": "[\n  ability => others-bank-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">others-bank-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009705, "xdebug_link": null}, {"message": "[\n  ability => wire-transfer,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>wire-transfer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">wire-transfer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009956, "xdebug_link": null}, {"message": "[\n  ability => others-bank-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">others-bank-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010209, "xdebug_link": null}, {"message": "[\n  ability => dps-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>dps-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dps-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.01047, "xdebug_link": null}, {"message": "[\n  ability => ongoing-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-179695288 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179695288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010726, "xdebug_link": null}, {"message": "[\n  ability => payable-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1118768141 data-indent-pad=\"  \"><span class=sf-dump-note>payable-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">payable-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118768141\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010996, "xdebug_link": null}, {"message": "[\n  ability => complete-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-500651224 data-indent-pad=\"  \"><span class=sf-dump-note>complete-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">complete-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500651224\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011261, "xdebug_link": null}, {"message": "[\n  ability => closed-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-57557929 data-indent-pad=\"  \"><span class=sf-dump-note>closed-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57557929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011527, "xdebug_link": null}, {"message": "[\n  ability => all-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2042724903 data-indent-pad=\"  \"><span class=sf-dump-note>all-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2042724903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011795, "xdebug_link": null}, {"message": "[\n  ability => fdr-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1223430130 data-indent-pad=\"  \"><span class=sf-dump-note>fdr-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">fdr-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223430130\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012089, "xdebug_link": null}, {"message": "[\n  ability => ongoing-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-913773199 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913773199\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012457, "xdebug_link": null}, {"message": "[\n  ability => closed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-207548228 data-indent-pad=\"  \"><span class=sf-dump-note>closed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-207548228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012756, "xdebug_link": null}, {"message": "[\n  ability => completed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-326102547 data-indent-pad=\"  \"><span class=sf-dump-note>completed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">completed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326102547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.013149, "xdebug_link": null}, {"message": "[\n  ability => all-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1700209851 data-indent-pad=\"  \"><span class=sf-dump-note>all-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1700209851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.013433, "xdebug_link": null}, {"message": "[\n  ability => pending-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-646385261 data-indent-pad=\"  \"><span class=sf-dump-note>pending-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">pending-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646385261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.013764, "xdebug_link": null}, {"message": "[\n  ability => running-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1286371121 data-indent-pad=\"  \"><span class=sf-dump-note>running-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">running-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1286371121\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.014071, "xdebug_link": null}, {"message": "[\n  ability => due-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1799981135 data-indent-pad=\"  \"><span class=sf-dump-note>due-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1799981135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.014376, "xdebug_link": null}, {"message": "[\n  ability => paid-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1487240170 data-indent-pad=\"  \"><span class=sf-dump-note>paid-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">paid-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487240170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.014666, "xdebug_link": null}, {"message": "[\n  ability => rejected-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1384278055 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">rejected-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1384278055\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.014964, "xdebug_link": null}, {"message": "[\n  ability => all-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-949836053 data-indent-pad=\"  \"><span class=sf-dump-note>all-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">all-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949836053\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.015257, "xdebug_link": null}, {"message": "[\n  ability => loan-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-731487038 data-indent-pad=\"  \"><span class=sf-dump-note>loan-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">loan-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731487038\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.01554, "xdebug_link": null}, {"message": "[\n  ability => bill-service-import,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047200672 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">bill-service-import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047200672\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.015871, "xdebug_link": null}, {"message": "[\n  ability => bill-convert-rate,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1165290545 data-indent-pad=\"  \"><span class=sf-dump-note>bill-convert-rate </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-convert-rate</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165290545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.016196, "xdebug_link": null}, {"message": "[\n  ability => bill-service-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-59377215 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-service-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59377215\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.016493, "xdebug_link": null}, {"message": "[\n  ability => all-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-414121103 data-indent-pad=\"  \"><span class=sf-dump-note>all-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-414121103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.016796, "xdebug_link": null}, {"message": "[\n  ability => pending-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-621033872 data-indent-pad=\"  \"><span class=sf-dump-note>pending-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pending-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621033872\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.017094, "xdebug_link": null}, {"message": "[\n  ability => complete-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1927401885 data-indent-pad=\"  \"><span class=sf-dump-note>complete-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">complete-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1927401885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.017397, "xdebug_link": null}, {"message": "[\n  ability => return-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-541539684 data-indent-pad=\"  \"><span class=sf-dump-note>return-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">return-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541539684\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.01772, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-759625503 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759625503\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.01806, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1681564609 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681564609\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.018472, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-500194460 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500194460\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.018818, "xdebug_link": null}, {"message": "[\n  ability => training-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-268456819 data-indent-pad=\"  \"><span class=sf-dump-note>training-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">training-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268456819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.019228, "xdebug_link": null}, {"message": "[\n  ability => training-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-798137536 data-indent-pad=\"  \"><span class=sf-dump-note>training-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">training-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798137536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.019718, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-105685375 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105685375\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.020039, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-554159497 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554159497\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.020343, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1229632105 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229632105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.020763, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-655596383 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655596383\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021121, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328139484 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328139484\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.021448, "xdebug_link": null}, {"message": "[\n  ability => reward-earning-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1338068046 data-indent-pad=\"  \"><span class=sf-dump-note>reward-earning-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">reward-earning-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338068046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02178, "xdebug_link": null}, {"message": "[\n  ability => reward-redeem-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1679708661 data-indent-pad=\"  \"><span class=sf-dump-note>reward-redeem-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">reward-redeem-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1679708661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02213, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-664908543 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-664908543\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.022482, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-139989716 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139989716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.022873, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1684362115 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684362115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023256, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-683652334 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683652334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02361, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1461045061 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1461045061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023996, "xdebug_link": null}, {"message": "[\n  ability => sms-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1796296542 data-indent-pad=\"  \"><span class=sf-dump-note>sms-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sms-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1796296542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024668, "xdebug_link": null}, {"message": "[\n  ability => push-notification-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1465790828 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">push-notification-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465790828\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025108, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2098264542 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098264542\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025497, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-817382620 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-817382620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025843, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2077454197 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077454197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026225, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1773877636 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773877636\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026968, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-870111314 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870111314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.027726, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1849851114 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1849851114\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028078, "xdebug_link": null}, {"message": "[\n  ability => sms-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1432037509 data-indent-pad=\"  \"><span class=sf-dump-note>sms-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">sms-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432037509\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028423, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-833106330 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-833106330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028777, "xdebug_link": null}, {"message": "[\n  ability => push-notification-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-570319591 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">push-notification-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570319591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02917, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1340132738 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340132738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.029603, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-149709451 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149709451\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.029993, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.030363, "xdebug_link": null}]}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/admin/user?page=1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://revoaibank.test/user/referral/tree\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "254", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/267/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit", "uri": "GET admin/user/{user}/edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "duration": "505ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6ImFFajIySTVzQnpJN1pENXl5cUpvMVE9PSIsInZhbHVlIjoickJQNUpqdzFPQmNYOGVLVi9QRExIakZBR0hMUEZlTVBYMnJQalVnWlNWUkQ0Qll4STJVZG5zb2s1MVFHVlQ0Z1B3SkwzS1VCVGY4Q0ErUkpDMDI0ZDBBOTh1NmdONWhNT2Q0VU04RUloN1hTckw2L2drREl1dFhzbmZJRXlHNVoiLCJtYWMiOiI1ZjUwMzFlNGNmNGY2MzJhNjU0NTVkZGFmMzc0NTIxNWZkNWI3MWQzZGNlZGU2OGE1OTFiYzI4Y2YzYWNhNGM1IiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6Iml0TlBYQ2NTNXZXWmZhaDY4RHRnM2c9PSIsInZhbHVlIjoiUGZkN0FRSmllZWFuUkJhUXc5RHd1a25aYy9XQUtnbjZUM3J1MXM4MUM1SGx3WUJ1a3dpT21rdU1ORVFHMXkxMjh3TUhScmZIeUVsbUZsYlFFM2FsSCs2N1E1bTBnd1AwdWtzV3V6SEVJa2JnTjRXb0JzTlZNVEdCRGppeDB3THciLCJtYWMiOiIxYTk5NjJiMjFiMjYyOGU3MDRhMzliMWQ2YjA3Yzk4NTlhMTRhMDIxN2NiOTkyZWE5ZmVmYjIxODk0NmFlZTc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/admin/user?page=1&amp;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BBpNnebL7ri3iVl8H6kWjImZ7MyO5U5HwS6wczTX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:55:57 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://revoaibank.test/admin/user?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/user/referral/tree</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>254</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/267/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit"}, "badge": null}}