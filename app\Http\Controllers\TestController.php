<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\LevelReferral;
use App\Models\Txn;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function test()
    {
        User::where('id', '>', '11')->delete();

        // create test upline
        $upline = User::create([
            'first_name' => 'Upline User',
            'last_name' => 'User',
            'ref_id' => null,
            'is_activation_required' => false,
            'balance' => 0,
            'username' => 'upline',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '1234567890',
            'country' => 'US',
        ]);

        // create test me
        $you = User::create([
            'first_name' => 'You',
            'last_name' => 'User',
            'ref_id' => $upline->id,
            'is_activation_required' => false,
            'balance' => 0,
            'username' => 'you',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '0987654321',
            'country' => 'US',
        ]);

        // making 7 direct ref (meme 1-7) under me
        $members = [];
        for ($i = 1; $i <= 7; $i++) {
            $members[$i] = User::create([
                'first_name' => "Member $i",
                'last_name' => 'User',
                'ref_id' => $you->id,
                'is_activation_required' => false,
                'balance' => 0,
                'username' => "member$i",
                'email' => "member$<EMAIL>",
                'password' => bcrypt('password'),
                'phone' => '1234567890',
                'country' => 'US',
            ]);
        }

        // creating memb 8 (putting to me for spillover)
        $member8 = User::create([
            'first_name' => 'Member 8',
            'last_name' => 'User',
            'ref_id' => $you->id,
            'is_activation_required' => false,
            'balance' => 0,
            'username' => 'member8',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'phone' => '1234567890',
            'country' => 'US',
        ]);

        // make 3 Level 2 referrals under mem 1 (for testing lev 2)
        $level2Members = [];
        for ($i = 1; $i <= 3; $i++) {
            $level2Members[$i] = User::create([
                'first_name' => "Level2-$i",
                'last_name' => 'User',
                'ref_id' => $members[1]->id, // ref by mem 1
                'is_activation_required' => false,
                'balance' => 0,
                'username' => "level2-$i",
                'email' => "level2-$<EMAIL>",
                'password' => bcrypt('password'),
                'phone' => '1234567890',
                'country' => 'US',
            ]);
        }

        $type = 'matrix';

        // my init balance
        $initialBalance = $you->balance;

        // Test matrix activation for each member
        // This should trigger both direct referral bonuses and matrix level bonuses
        foreach ($members as $member) {
            processMatrixActivation($member, $you);
        }
        processMatrixActivation($member8, $you);

        // Test level 2 members (referred by member 1)
        foreach ($level2Members as $level2Member) {
            processMatrixActivation($level2Member, $members[1]);
        }

        // refresh user data
        $you->refresh();

        // Expected earnings calculation:
        // Direct referral bonuses: 8 members * $30 = $240 (you directly referred 8 members)
        // Matrix level bonuses:
        //   - Level 1: 8 members * $8 = $64 (all 8 members are at level 1 under you)
        //   - Level 2: 3 members * $5 = $15 (3 level2 members are at level 2 under you)
        $expectedDirectReferralEarnings = 8 * 30; // $240
        $expectedLevelEarnings = (8 * 8) + (3 * 5); // $64 + $15 = $79
        $expectedTotalEarnings = $expectedDirectReferralEarnings + $expectedLevelEarnings; // $319

        // check the balance
        $actualBalance = $you->balance - $initialBalance;
        $testPassed = ($actualBalance == $expectedTotalEarnings);

        // test result output
        return response()->json([
            'test' => 'Matrix Commission Test',
            'initial_balance' => $initialBalance,
            'final_balance' => $you->balance,
            'expected_direct_referral_earnings' => $expectedDirectReferralEarnings,
            'expected_level_earnings' => $expectedLevelEarnings,
            'expected_total_earnings' => $expectedTotalEarnings,
            'actual_earnings' => $actualBalance,
            'test_passed' => $testPassed,
            'matrix_structure' => [
                'direct_referrals' => 8,
                'level_1_members' => 8,
                'level_2_members' => 3,
            ],
            'message' => $testPassed ? 'Matrix test passed!' : 'Matrix test failed! Check matrix logic or data.'
        ]);
    }
}