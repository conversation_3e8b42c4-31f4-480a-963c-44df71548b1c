{"__meta": {"id": "01JZ4Q9ZC252218PHJGGP1RR7Q", "datetime": "2025-07-02 04:46:16", "utime": **********.962891, "method": "GET", "uri": "/user/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[04:46:16] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in E:\\laragon\\www\\revoaibank\\app\\Models\\User.php on line 340", "message_html": null, "is_string": false, "label": "warning", "time": **********.646269, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.326266, "end": **********.962918, "duration": 0.****************, "duration_str": "637ms", "measures": [{"label": "Booting", "start": **********.326266, "relative_start": 0, "end": **********.585919, "relative_end": **********.585919, "duration": 0.*****************, "duration_str": "260ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.585927, "relative_start": 0.****************, "end": **********.96292, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.593063, "relative_start": 0.****************, "end": **********.595347, "relative_end": **********.595347, "duration": 0.002283811569213867, "duration_str": "2.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.649188, "relative_start": 0.****************, "end": **********.961563, "relative_end": **********.961563, "duration": 0.****************, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 30, "nb_templates": 30, "templates": [{"name": "1x frontend::user.dashboard", "param_count": null, "params": [], "start": **********.660769, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/dashboard.blade.phpfrontend::user.dashboard", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Fdashboard.blade.php:1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.dashboard"}, {"name": "1x frontend::layouts.user", "param_count": null, "params": [], "start": **********.726074, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/layouts/user.blade.phpfrontend::layouts.user", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Flayouts%2Fuser.blade.php:1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::layouts.user"}, {"name": "1x frontend::user.include.__head", "param_count": null, "params": [], "start": **********.733776, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.phpfrontend::user.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.74882, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x frontend::include.__user_side_nav", "param_count": null, "params": [], "start": **********.75432, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.phpfrontend::include.__user_side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:1", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_side_nav"}, {"name": "18x frontend::include.__menu-item", "param_count": null, "params": [], "start": **********.774581, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__menu-item.blade.phpfrontend::include.__menu-item", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__menu-item.blade.php:1", "ajax": false, "filename": "__menu-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "frontend::include.__menu-item"}, {"name": "1x frontend::include.__user_header", "param_count": null, "params": [], "start": **********.891133, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.phpfrontend::include.__user_header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:1", "ajax": false, "filename": "__user_header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__user_header"}, {"name": "1x frontend.default.include.__user-notification-data", "param_count": null, "params": [], "start": **********.913508, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/frontend/default/include/__user-notification-data.blade.phpfrontend.default.include.__user-notification-data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Finclude%2F__user-notification-data.blade.php:1", "ajax": false, "filename": "__user-notification-data.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.default.include.__user-notification-data"}, {"name": "1x frontend::include.__kyc_warning", "param_count": null, "params": [], "start": **********.922863, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__kyc_warning.blade.phpfrontend::include.__kyc_warning", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__kyc_warning.blade.php:1", "ajax": false, "filename": "__kyc_warning.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__kyc_warning"}, {"name": "1x frontend::include.__matrix-activation", "param_count": null, "params": [], "start": **********.928191, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__matrix-activation.blade.phpfrontend::include.__matrix-activation", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__matrix-activation.blade.php:1", "ajax": false, "filename": "__matrix-activation.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::include.__matrix-activation"}, {"name": "1x frontend::user.include.__script", "param_count": null, "params": [], "start": **********.940513, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__script.blade.phpfrontend::user.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend::user.include.__script"}, {"name": "1x global.__t_notify", "param_count": null, "params": [], "start": **********.950399, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__t_notify.blade.phpglobal.__t_notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__t_notify.blade.php:1", "ajax": false, "filename": "__t_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__t_notify"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.958483, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET user/dashboard", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, App\\Http\\Middleware\\AllowIframeMiddleware", "controller": "App\\Http\\Controllers\\Frontend\\DashboardController@dashboard<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "user.dashboard", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/DashboardController.php:22-62</a>"}, "queries": {"count": 40, "nb_statements": 40, "nb_visible_statements": 40, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02462, "accumulated_duration_str": "24.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 228 limit 1", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.603122, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 3.128}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 228 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [228, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "2fa", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\TwoFaCheck.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 18, "namespace": "middleware", "name": "install_check", "file": "E:\\laragon\\www\\revoaibank\\vendor\\remotelywork\\installer\\src\\Http\\Middleware\\InstallCheck.php", "line": 17}], "start": **********.613053, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "isActive:23", "source": {"index": 14, "namespace": "middleware", "name": "isActive", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Middleware\\CheckDeactivate.php", "line": 23}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FMiddleware%2FCheckDeactivate.php:23", "ajax": false, "filename": "CheckDeactivate.php", "line": "23"}, "connection": "revoaibank", "explain": null, "start_percent": 3.128, "width_percent": 2.884}, {"sql": "select * from `transactions` where `user_id` = 228 order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 27}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.616098, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:27", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 27}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:27", "ajax": false, "filename": "DashboardController.php", "line": "27"}, "connection": "revoaibank", "explain": null, "start_percent": 6.011, "width_percent": 2.153}, {"sql": "select * from `referral_programs`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 29}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.61747, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "User.php:244", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:244", "ajax": false, "filename": "User.php", "line": "244"}, "connection": "revoaibank", "explain": null, "start_percent": 8.164, "width_percent": 2.884}, {"sql": "select * from `referral_links` where (`user_id` = 228 and `referral_program_id` = 1) limit 1", "type": "query", "params": [], "bindings": [228, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 29}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6188738, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ReferralLink.php:17", "source": {"index": 20, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralLink.php:17", "ajax": false, "filename": "ReferralLink.php", "line": "17"}, "connection": "revoaibank", "explain": null, "start_percent": 11.048, "width_percent": 3.087}, {"sql": "insert into `referral_links` (`user_id`, `referral_program_id`, `code`, `updated_at`, `created_at`) values (228, 1, '7tP9nsXTlV', '2025-07-02 04:46:16', '2025-07-02 04:46:16')", "type": "query", "params": [], "bindings": [228, 1, "7tP9nsXTlV", "2025-07-02 04:46:16", "2025-07-02 04:46:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, {"index": 26, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 245}, {"index": 31, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 244}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 29}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.622422, "duration": 0.0063, "duration_str": "6.3ms", "memory": 0, "memory_str": null, "filename": "ReferralLink.php:17", "source": {"index": 25, "namespace": null, "name": "app/Models/ReferralLink.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\ReferralLink.php", "line": 17}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralLink.php:17", "ajax": false, "filename": "ReferralLink.php", "line": "17"}, "connection": "revoaibank", "explain": null, "start_percent": 14.135, "width_percent": 25.589}, {"sql": "select * from `loans` where `loans`.`user_id` = 228 and `loans`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 31}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.62992, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:31", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:31", "ajax": false, "filename": "DashboardController.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 39.724, "width_percent": 1.706}, {"sql": "select * from `loan_transactions` where `loan_id` is null order by `installment_date` desc limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.631141, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:31", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:31", "ajax": false, "filename": "DashboardController.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 41.43, "width_percent": 1.381}, {"sql": "select count(*) as aggregate from `transactions` where `user_id` = 228 limit 5", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6318822, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:34", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:34", "ajax": false, "filename": "DashboardController.php", "line": "34"}, "connection": "revoaibank", "explain": null, "start_percent": 42.811, "width_percent": 1.097}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'deposit' or `type` = 'manual_deposit')", "type": "query", "params": [], "bindings": [228, "success", "deposit", "manual_deposit"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 35}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.632858, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "User.php:259", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:259", "ajax": false, "filename": "User.php", "line": "259"}, "connection": "revoaibank", "explain": null, "start_percent": 43.907, "width_percent": 1.584}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [228, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.633822, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 45.491, "width_percent": 1.543}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment')) and `created_at` >= '2025-06-25 04:46:16'", "type": "query", "params": [], "bindings": [228, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment", "2025-06-25 04:46:16"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 37}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.634718, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 47.035, "width_percent": 6.336}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'withdraw' or `type` = 'withdraw_auto')", "type": "query", "params": [], "bindings": [228, "success", "withdraw", "withdraw_auto"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 38}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.636761, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "User.php:280", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:280", "ajax": false, "filename": "User.php", "line": "280"}, "connection": "revoaibank", "explain": null, "start_percent": 53.371, "width_percent": 1.3}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'fund_transfer')", "type": "query", "params": [], "bindings": [228, "success", "fund_transfer"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.63749, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:289", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:289", "ajax": false, "filename": "User.php", "line": "289"}, "connection": "revoaibank", "explain": null, "start_percent": 54.671, "width_percent": 1.178}, {"sql": "select * from `dps` where `dps`.`user_id` = 228 and `dps`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 40}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.638438, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:40", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:40", "ajax": false, "filename": "DashboardController.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 55.849, "width_percent": 1.381}, {"sql": "select * from `bills` where `bills`.`user_id` = 228 and `bills`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 41}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.639414, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:41", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:41", "ajax": false, "filename": "DashboardController.php", "line": "41"}, "connection": "revoaibank", "explain": null, "start_percent": 57.23, "width_percent": 1.3}, {"sql": "select * from `fdr` where `fdr`.`user_id` = 228 and `fdr`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6403701, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:42", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:42", "ajax": false, "filename": "DashboardController.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 58.53, "width_percent": 1.34}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 228 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1') limit 1", "type": "query", "params": [], "bindings": [228, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.641233, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:43", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 43}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:43", "ajax": false, "filename": "DashboardController.php", "line": "43"}, "connection": "revoaibank", "explain": null, "start_percent": 59.87, "width_percent": 1.381}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'referral')", "type": "query", "params": [], "bindings": [228, "success", "referral"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 298}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.642684, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "User.php:298", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 298}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:298", "ajax": false, "filename": "User.php", "line": "298"}, "connection": "revoaibank", "explain": null, "start_percent": 61.251, "width_percent": 1.503}, {"sql": "select count(*) as aggregate from `referral_relationships` where `referral_relationships`.`referral_link_id` = 23 and `referral_relationships`.`referral_link_id` is not null", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.643725, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:50", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 50}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:50", "ajax": false, "filename": "DashboardController.php", "line": "50"}, "connection": "revoaibank", "explain": null, "start_percent": 62.754, "width_percent": 2.071}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 228 and `transactions`.`user_id` is not null and `status` = 'success' and (`target_id` is not null and `target_type` = 'deposit' and `type` = 'referral')", "type": "query", "params": [], "bindings": [228, "success", "deposit", "referral"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 270}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 51}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.644698, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "User.php:270", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 270}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:270", "ajax": false, "filename": "User.php", "line": "270"}, "connection": "revoaibank", "explain": null, "start_percent": 64.825, "width_percent": 1.381}, {"sql": "select * from `tickets` where `tickets`.`user_id` = 228 and `tickets`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.647421, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:53", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/DashboardController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Frontend\\DashboardController.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:53", "ajax": false, "filename": "DashboardController.php", "line": "53"}, "connection": "revoaibank", "explain": null, "start_percent": 66.206, "width_percent": 1.422}, {"sql": "select * from `login_activities` where `login_activities`.`user_id` = 228 and `login_activities`.`user_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::user.dashboard", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/dashboard.blade.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.673044, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "frontend::user.dashboard:37", "source": {"index": 20, "namespace": "view", "name": "frontend::user.dashboard", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/dashboard.blade.php", "line": 37}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Fdashboard.blade.php:37", "ajax": false, "filename": "dashboard.blade.php", "line": "37"}, "connection": "revoaibank", "explain": null, "start_percent": 67.628, "width_percent": 2.843}, {"sql": "select * from `currencies` where `is_default` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 831}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.684154, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "helpers.php:831", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 831}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:831", "ajax": false, "filename": "helpers.php", "line": "831"}, "connection": "revoaibank", "explain": null, "start_percent": 70.471, "width_percent": 1.097}, {"sql": "select * from `currencies` where `is_default` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 831}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.68502, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "helpers.php:831", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 831}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:831", "ajax": false, "filename": "helpers.php", "line": "831"}, "connection": "revoaibank", "explain": null, "start_percent": 71.568, "width_percent": 0.894}, {"sql": "select * from `languages` where `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["en"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.727025, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "helpers.php:636", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 636}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:636", "ajax": false, "filename": "helpers.php", "line": "636"}, "connection": "revoaibank", "explain": null, "start_percent": 72.461, "width_percent": 5.158}, {"sql": "select * from `custom_css` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.738893, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "frontend::user.include.__head:24", "source": {"index": 19, "namespace": "view", "name": "frontend::user.include.__head", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/user/include/__head.blade.php", "line": 24}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Fuser%2Finclude%2F__head.blade.php:24", "ajax": false, "filename": "__head.blade.php", "line": "24"}, "connection": "revoaibank", "explain": null, "start_percent": 77.62, "width_percent": 1.868}, {"sql": "select count(*) as aggregate from `dps` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7642012, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:673", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 673}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:673", "ajax": false, "filename": "helpers.php", "line": "673"}, "connection": "revoaibank", "explain": null, "start_percent": 79.488, "width_percent": 1.99}, {"sql": "select count(*) as aggregate from `fdr` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.765178, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "helpers.php:681", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 681}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:681", "ajax": false, "filename": "helpers.php", "line": "681"}, "connection": "revoaibank", "explain": null, "start_percent": 81.478, "width_percent": 1.219}, {"sql": "select count(*) as aggregate from `loans` where `status` = 'running' and `user_id` = 228", "type": "query", "params": [], "bindings": ["running", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.765915, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "helpers.php:690", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 690}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:690", "ajax": false, "filename": "helpers.php", "line": "690"}, "connection": "revoaibank", "explain": null, "start_percent": 82.697, "width_percent": 1.056}, {"sql": "select count(*) as aggregate from `tickets` where `status` = 'open' and `user_id` = 228", "type": "query", "params": [], "bindings": ["open", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.7670538, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:13", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 13}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:13", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "13"}, "connection": "revoaibank", "explain": null, "start_percent": 83.753, "width_percent": 1.056}, {"sql": "select * from `users` where `users`.`ref_id` = 228 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [228], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 14}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.767757, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:14", "source": {"index": 20, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 14}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:14", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "14"}, "connection": "revoaibank", "explain": null, "start_percent": 84.809, "width_percent": 1.422}, {"sql": "select * from `user_navigations` order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.768762, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_side_nav:15", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_side_nav", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_side_nav.blade.php", "line": 15}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_side_nav.blade.php:15", "ajax": false, "filename": "__user_side_nav.blade.php", "line": "15"}, "connection": "revoaibank", "explain": null, "start_percent": 86.231, "width_percent": 2.112}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 228 order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["user", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.905668, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:32", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 32}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:32", "ajax": false, "filename": "__user_header.blade.php", "line": "32"}, "connection": "revoaibank", "explain": null, "start_percent": 88.343, "width_percent": 2.924}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'user' and `user_id` = 228 and `read` = 0", "type": "query", "params": [], "bindings": ["user", 228, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.906943, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:33", "source": {"index": 16, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 33}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:33", "ajax": false, "filename": "__user_header.blade.php", "line": "33"}, "connection": "revoaibank", "explain": null, "start_percent": 91.267, "width_percent": 1.3}, {"sql": "select * from `notifications` where `for` = 'user' and `user_id` = 228", "type": "query", "params": [], "bindings": ["user", 228], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9076731, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:34", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:34", "ajax": false, "filename": "__user_header.blade.php", "line": "34"}, "connection": "revoaibank", "explain": null, "start_percent": 92.567, "width_percent": 1.178}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9164798, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "frontend::include.__user_header:45", "source": {"index": 15, "namespace": "view", "name": "frontend::include.__user_header", "file": "E:\\laragon\\www\\revoaibank\\app\\Providers/../../resources/views/frontend/corporate/include/__user_header.blade.php", "line": 45}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Ffrontend%2Fcorporate%2Finclude%2F__user_header.blade.php:45", "ajax": false, "filename": "__user_header.blade.php", "line": "45"}, "connection": "revoaibank", "explain": null, "start_percent": 93.745, "width_percent": 1.706}, {"sql": "select * from `plugins` where `name` = 'Google Analytics' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Google Analytics", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9588962, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 95.451, "width_percent": 2.559}, {"sql": "select * from `plugins` where `name` = '<PERSON><PERSON><PERSON>' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Tawk Chat", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.9600809, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 98.01, "width_percent": 1.015}, {"sql": "select * from `plugins` where `name` = 'Facebook Messenger' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["Facebook Messenger", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.960765, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:195", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\revoaibank\\app\\helpers.php", "line": 195}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2Fhelpers.php:195", "ajax": false, "filename": "helpers.php", "line": "195"}, "connection": "revoaibank", "explain": null, "start_percent": 99.025, "width_percent": 0.975}]}, "models": {"data": {"App\\Models\\UserNavigation": {"value": 18, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserNavigation.php:1", "ajax": false, "filename": "UserNavigation.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 5, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FTransaction.php:1", "ajax": false, "filename": "Transaction.php", "line": "?"}}, "App\\Models\\User": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Language": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\ReferralProgram": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FReferralProgram.php:1", "ajax": false, "filename": "ReferralProgram.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\CustomCss": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCustomCss.php:1", "ajax": false, "filename": "CustomCss.php", "line": "?"}}}, "count": 32, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/admin/user/login/228\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01JZ4Q9YQVCRT4N89906VCEGHK\" => null\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "228"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/user/dashboard", "action_name": "user.dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\DashboardController@dashboard", "uri": "GET user/dashboard", "controller": "App\\Http\\Controllers\\Frontend\\DashboardController@dashboard<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/user", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FFrontend%2FDashboardController.php:22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/DashboardController.php:22-62</a>", "middleware": "web, XSS, translate, trans, isDemo, install_check, auth, 2fa, isActive, web, web, App\\Http\\Middleware\\AllowIframeMiddleware", "duration": "639ms", "peak_memory": "30MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6Ikc2YUZOK3VaQnQwTDJKR2MrbXhPVGc9PSIsInZhbHVlIjoiNWtZKzZaV0FVQzBmWit2RjFRZ3d4MmVXV2Z6azE0U0FsNFUvMDJHZU1KeHI5QWV5d2RYUzU0Yjl0dmR0ODF4T3hjNkx6V09uRWRUSXk4N0tRUEk3SzJQckxzMmVQa1pBaDlJSy9VY0hJYWRUNUVheDIvU3Z5ZWtTc0xNY3F0WisiLCJtYWMiOiJjMGYxMzMwZDY5ZGU4MGY2NWMzYzBlNjUzODA0OGU3YTUyYzk0MThjM2VhZWMxYTZhOWYzYTMzNGZlMTU2OGMyIiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6ImZMNWdHMVhyOUhJSUJTaGRzaXVPUXc9PSIsInZhbHVlIjoiQkQ4R1U1UkVFa3VhekpabWhQaXdFMmtoeUcrMWgzL0xRRkdqUkpYNTVoR2JVcmVqU3czeDZPRWhNckplcTVOdDFLQ1dRY3MwWmpKbHQ1amllTHZxbHdOK2M5eUExUDU4OWx6RUllaTQyNmxUTWtqYlQzZ01iZkNIV292U2c2Sk0iLCJtYWMiOiJkMTM5NWQ1ZDZhMmJjNjY4MmNhOGJiMGI2ZjJkM2NiNjlkZTljNzFlODI2MmU1OTA0YjQyYmY0Mzk3YWU0OTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://revoaibank.test/admin/user/228/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LQmddLR34V5gx8fxxD8s2W20y4FntkShwkOAEGlR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:46:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-frame-options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">ALLOWALL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">https://revoaibank.test/admin/user/login/228</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ4Q9YQVCRT4N89906VCEGHK</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>228</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/user/dashboard", "action_name": "user.dashboard", "controller_action": "App\\Http\\Controllers\\Frontend\\DashboardController@dashboard"}, "badge": null}}