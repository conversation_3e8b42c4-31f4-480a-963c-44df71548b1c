{"__meta": {"id": "01JZ4QN6BTPXPDTHTDCNH593SD", "datetime": "2025-07-02 04:52:24", "utime": **********.571167, "method": "GET", "uri": "/admin/user/254/edit", "ip": "127.0.0.1"}, "php": {"version": "8.3.19", "interface": "cgi-fcgi"}, "messages": {"count": 1, "messages": [{"message": "[04:52:24] LOG.warning: ucwords(): Passing null to parameter #1 ($string) of type string is deprecated in E:\\laragon\\www\\revoaibank\\storage\\framework\\views\\53c2689336b2ebae9d0d54e44a025e7d.php on line 47", "message_html": null, "is_string": false, "label": "warning", "time": **********.372683, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.028881, "end": **********.571191, "duration": 0.****************, "duration_str": "542ms", "measures": [{"label": "Booting", "start": **********.028881, "relative_start": 0, "end": **********.282917, "relative_end": **********.282917, "duration": 0.*****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.282927, "relative_start": 0.****************, "end": **********.571193, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.290311, "relative_start": 0.***************, "end": **********.295249, "relative_end": **********.295249, "duration": 0.004937887191772461, "duration_str": "4.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.362059, "relative_start": 0.****************, "end": **********.569588, "relative_end": **********.569588, "duration": 0.*****************, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x backend.user.edit", "param_count": null, "params": [], "start": **********.368432, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.phpbackend.user.edit", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.edit"}, {"name": "1x backend.user.include.__delete_popup", "param_count": null, "params": [], "start": **********.382023, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__delete_popup.blade.phpbackend.user.include.__delete_popup", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__delete_popup.blade.php:1", "ajax": false, "filename": "__delete_popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__delete_popup"}, {"name": "1x backend.user.include.__status_update", "param_count": null, "params": [], "start": **********.391299, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__status_update.blade.phpbackend.user.include.__status_update", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__status_update.blade.php:1", "ajax": false, "filename": "__status_update.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__status_update"}, {"name": "1x backend.user.include.__basic_info", "param_count": null, "params": [], "start": **********.401921, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__basic_info.blade.phpbackend.user.include.__basic_info", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__basic_info.blade.php:1", "ajax": false, "filename": "__basic_info.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__basic_info"}, {"name": "1x backend.user.include.__earnings", "param_count": null, "params": [], "start": **********.411149, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__earnings.blade.phpbackend.user.include.__earnings", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__earnings.blade.php:1", "ajax": false, "filename": "__earnings.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__earnings"}, {"name": "1x backend.user.include.__dps", "param_count": null, "params": [], "start": **********.416223, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__dps.blade.phpbackend.user.include.__dps", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__dps.blade.php:1", "ajax": false, "filename": "__dps.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__dps"}, {"name": "1x backend.user.include.__fdr", "param_count": null, "params": [], "start": **********.420993, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__fdr.blade.phpbackend.user.include.__fdr", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__fdr.blade.php:1", "ajax": false, "filename": "__fdr.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__fdr"}, {"name": "1x backend.user.include.__loan", "param_count": null, "params": [], "start": **********.425852, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__loan.blade.phpbackend.user.include.__loan", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__loan.blade.php:1", "ajax": false, "filename": "__loan.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__loan"}, {"name": "1x backend.user.include.__card", "param_count": null, "params": [], "start": **********.432962, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__card.blade.phpbackend.user.include.__card", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__card.blade.php:1", "ajax": false, "filename": "__card.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__card"}, {"name": "1x backend.user.include.__transactions", "param_count": null, "params": [], "start": **********.437846, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__transactions.blade.phpbackend.user.include.__transactions", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__transactions.blade.php:1", "ajax": false, "filename": "__transactions.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__transactions"}, {"name": "1x backend.user.include.__referral_tree", "param_count": null, "params": [], "start": **********.442541, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__referral_tree.blade.phpbackend.user.include.__referral_tree", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__referral_tree.blade.php:1", "ajax": false, "filename": "__referral_tree.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__referral_tree"}, {"name": "1x backend.user.include.__ticket", "param_count": null, "params": [], "start": **********.447359, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__ticket.blade.phpbackend.user.include.__ticket", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__ticket.blade.php:1", "ajax": false, "filename": "__ticket.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__ticket"}, {"name": "1x backend.user.include.__mail_send", "param_count": null, "params": [], "start": **********.452156, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__mail_send.blade.phpbackend.user.include.__mail_send", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__mail_send.blade.php:1", "ajax": false, "filename": "__mail_send.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__mail_send"}, {"name": "1x backend.user.include.__balance", "param_count": null, "params": [], "start": **********.456819, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/user/include/__balance.blade.phpbackend.user.include.__balance", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Finclude%2F__balance.blade.php:1", "ajax": false, "filename": "__balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.user.include.__balance"}, {"name": "1x backend.layouts.app", "param_count": null, "params": [], "start": **********.468348, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/layouts/app.blade.phpbackend.layouts.app", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Flayouts%2Fapp.blade.php:1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.layouts.app"}, {"name": "1x backend.include.__head", "param_count": null, "params": [], "start": **********.473165, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__head.blade.phpbackend.include.__head", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__head.blade.php:1", "ajax": false, "filename": "__head.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__head"}, {"name": "1x global._notify", "param_count": null, "params": [], "start": **********.484727, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/_notify.blade.phpglobal._notify", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F_notify.blade.php:1", "ajax": false, "filename": "_notify.blade.php", "line": "?"}, "render_count": 1, "name_original": "global._notify"}, {"name": "1x backend.include.__header", "param_count": null, "params": [], "start": **********.489917, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.phpbackend.include.__header", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:1", "ajax": false, "filename": "__header.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__header"}, {"name": "1x global.__notification_data", "param_count": null, "params": [], "start": **********.519666, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_data.blade.phpglobal.__notification_data", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_data.blade.php:1", "ajax": false, "filename": "__notification_data.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_data"}, {"name": "1x backend.include.__side_nav", "param_count": null, "params": [], "start": **********.527657, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__side_nav.blade.phpbackend.include.__side_nav", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__side_nav.blade.php:1", "ajax": false, "filename": "__side_nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__side_nav"}, {"name": "1x backend.include.__script", "param_count": null, "params": [], "start": **********.564282, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__script.blade.phpbackend.include.__script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__script.blade.php:1", "ajax": false, "filename": "__script.blade.php", "line": "?"}, "render_count": 1, "name_original": "backend.include.__script"}, {"name": "1x global.__notification_script", "param_count": null, "params": [], "start": **********.569022, "type": "blade", "hash": "bladeE:\\laragon\\www\\revoaibank\\resources\\views/global/__notification_script.blade.phpglobal.__notification_script", "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fglobal%2F__notification_script.blade.php:1", "ajax": false, "filename": "__notification_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "global.__notification_script"}]}, "route": {"uri": "GET admin/user/{user}/edit", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check, permission:customer-basic-manage|customer-change-password|all-type-status|customer-balance-add-or-subtract", "as": "admin.user.edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.015700000000000002, "accumulated_duration_str": "15.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `admins` where `id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.305037, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "revoaibank", "explain": null, "start_percent": 0, "width_percent": 4.14}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 11 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": [11, "App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3189828, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php:305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "revoaibank", "explain": null, "start_percent": 4.14, "width_percent": 4.968}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (11) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\Admin'", "type": "query", "params": [], "bindings": ["App\\Models\\Admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.320862, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php:188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "revoaibank", "explain": null, "start_percent": 9.108, "width_percent": 2.866}, {"sql": "select * from `users` where `users`.`id` = '254' limit 1", "type": "query", "params": [], "bindings": ["254"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.325349, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "UserController.php:201", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 201}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:201", "ajax": false, "filename": "UserController.php", "line": "201"}, "connection": "revoaibank", "explain": null, "start_percent": 11.975, "width_percent": 3.057}, {"sql": "select max(`the_order`) as aggregate from `level_referrals` where `type` = 'investment'", "type": "query", "params": [], "bindings": ["investment"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.326872, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "UserController.php:202", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 202}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:202", "ajax": false, "filename": "UserController.php", "line": "202"}, "connection": "revoaibank", "explain": null, "start_percent": 15.032, "width_percent": 3.376}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 254 and `user_wallets`.`user_id` is not null and exists (select * from `currencies` where `user_wallets`.`currency_id` = `currencies`.`id` and `is_default` = '1')) as `exists`", "type": "query", "params": [], "bindings": [254, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.330296, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "UserController.php:214", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 214}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:214", "ajax": false, "filename": "UserController.php", "line": "214"}, "connection": "revoaibank", "explain": null, "start_percent": 18.408, "width_percent": 4.713}, {"sql": "select `id` from `currencies` where `is_default` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.331718, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "User.php:401", "source": {"index": 17, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 401}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:401", "ajax": false, "filename": "User.php", "line": "401"}, "connection": "revoaibank", "explain": null, "start_percent": 23.121, "width_percent": 1.401}, {"sql": "insert into `user_wallets` (`balance`, `currency_id`, `user_id`, `updated_at`, `created_at`) values (0, 36, 254, '2025-07-02 04:52:24', '2025-07-02 04:52:24')", "type": "query", "params": [], "bindings": [0, 36, 254, "2025-07-02 04:52:24", "2025-07-02 04:52:24"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 215}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.332617, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "User.php:399", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 399}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:399", "ajax": false, "filename": "User.php", "line": "399"}, "connection": "revoaibank", "explain": null, "start_percent": 24.522, "width_percent": 22.038}, {"sql": "select * from `branches` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.336981, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "UserController.php:333", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 333}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:333", "ajax": false, "filename": "UserController.php", "line": "333"}, "connection": "revoaibank", "explain": null, "start_percent": 46.561, "width_percent": 2.42}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 254 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'deposit' or `type` = 'manual_deposit')", "type": "query", "params": [], "bindings": [254, "success", "deposit", "manual_deposit"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 196}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 336}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.339986, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "User.php:259", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 259}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:259", "ajax": false, "filename": "User.php", "line": "259"}, "connection": "revoaibank", "explain": null, "start_percent": 48.981, "width_percent": 2.866}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 254 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'fund_transfer')", "type": "query", "params": [], "bindings": [254, "success", "fund_transfer"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 337}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3408332, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:289", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 289}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:289", "ajax": false, "filename": "User.php", "line": "289"}, "connection": "revoaibank", "explain": null, "start_percent": 51.847, "width_percent": 1.847}, {"sql": "select * from `dps` where `dps`.`user_id` = 254 and `dps`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.341818, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "UserController.php:338", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 338}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:338", "ajax": false, "filename": "UserController.php", "line": "338"}, "connection": "revoaibank", "explain": null, "start_percent": 53.694, "width_percent": 2.229}, {"sql": "select * from `fdr` where `fdr`.`user_id` = 254 and `fdr`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3429382, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "UserController.php:339", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 339}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:339", "ajax": false, "filename": "UserController.php", "line": "339"}, "connection": "revoaibank", "explain": null, "start_percent": 55.924, "width_percent": 2.93}, {"sql": "select * from `loans` where `loans`.`user_id` = 254 and `loans`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.344172, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "UserController.php:340", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 340}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:340", "ajax": false, "filename": "UserController.php", "line": "340"}, "connection": "revoaibank", "explain": null, "start_percent": 58.854, "width_percent": 2.357}, {"sql": "select * from `bills` where `bills`.`user_id` = 254 and `bills`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.345223, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "UserController.php:341", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 341}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:341", "ajax": false, "filename": "UserController.php", "line": "341"}, "connection": "revoaibank", "explain": null, "start_percent": 61.21, "width_percent": 2.611}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 254 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` = 'withdraw' or `type` = 'withdraw_auto')", "type": "query", "params": [], "bindings": [254, "success", "withdraw", "withdraw_auto"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 342}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.346119, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "User.php:280", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 280}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:280", "ajax": false, "filename": "User.php", "line": "280"}, "connection": "revoaibank", "explain": null, "start_percent": 63.822, "width_percent": 2.166}, {"sql": "select * from `tickets` where `tickets`.`user_id` = 254 and `tickets`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.347724, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "UserController.php:343", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 343}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:343", "ajax": false, "filename": "UserController.php", "line": "343"}, "connection": "revoaibank", "explain": null, "start_percent": 65.987, "width_percent": 2.357}, {"sql": "select sum(`amount`) as aggregate from `transactions` where `transactions`.`user_id` = 254 and `transactions`.`user_id` is not null and `status` = 'success' and (`type` in ('referral', 'signup_bonus', 'portfolio_bonus', 'reward_redeem', 'dps_maturity', 'fdr_installment'))", "type": "query", "params": [], "bindings": [254, "success", "referral", "signup_bonus", "portfolio_bonus", "reward_redeem", "dps_maturity", "fdr_installment"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 345}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.348589, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "User.php:217", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Models\\User.php", "line": 217}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:217", "ajax": false, "filename": "User.php", "line": "217"}, "connection": "revoaibank", "explain": null, "start_percent": 68.344, "width_percent": 3.185}, {"sql": "select count(*) as aggregate from `users` where `users`.`ref_id` = 254 and `users`.`ref_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3494918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "UserController.php:346", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 346}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:346", "ajax": false, "filename": "UserController.php", "line": "346"}, "connection": "revoaibank", "explain": null, "start_percent": 71.529, "width_percent": 1.592}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 254 and `user_wallets`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.359159, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 73.121, "width_percent": 3.057}, {"sql": "select * from `currencies` where `currencies`.`id` in (36)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 351}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.360206, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "UserController.php:382", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Backend/UserController.php", "file": "E:\\laragon\\www\\revoaibank\\app\\Http\\Controllers\\Backend\\UserController.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:382", "ajax": false, "filename": "UserController.php", "line": "382"}, "connection": "revoaibank", "explain": null, "start_percent": 76.178, "width_percent": 1.529}, {"sql": "select * from `login_activities` where `login_activities`.`user_id` = 254 and `login_activities`.`user_id` is not null", "type": "query", "params": [], "bindings": [254], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.373427, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "backend.user.edit:50", "source": {"index": 20, "namespace": "view", "name": "backend.user.edit", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/user/edit.blade.php", "line": 50}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Fuser%2Fedit.blade.php:50", "ajax": false, "filename": "edit.blade.php", "line": "50"}, "connection": "revoaibank", "explain": null, "start_percent": 77.707, "width_percent": 4.459}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5080712, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:31", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 31}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:31", "ajax": false, "filename": "__header.blade.php", "line": "31"}, "connection": "revoaibank", "explain": null, "start_percent": 82.166, "width_percent": 5.223}, {"sql": "select * from `notifications` where `for` = 'admin' order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.510184, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 87.389, "width_percent": 4.013}, {"sql": "select * from `users` where `users`.`id` in (179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.511509, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:40", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 40}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:40", "ajax": false, "filename": "__header.blade.php", "line": "40"}, "connection": "revoaibank", "explain": null, "start_percent": 91.401, "width_percent": 2.229}, {"sql": "select count(*) as aggregate from `notifications` where `for` = 'admin' and `read` = 0", "type": "query", "params": [], "bindings": ["admin", 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.5123632, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:41", "source": {"index": 16, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 41}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:41", "ajax": false, "filename": "__header.blade.php", "line": "41"}, "connection": "revoaibank", "explain": null, "start_percent": 93.631, "width_percent": 1.847}, {"sql": "select * from `notifications` where `for` = 'admin'", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.51313, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 15, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 95.478, "width_percent": 2.357}, {"sql": "select * from `users` where `users`.`id` in (1, 2, 3, 4, 11, 177, 178, 179, 180, 181, 182, 221, 222, 223, 224, 225, 227)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "E:\\laragon\\www\\revoaibank\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.514281, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "backend.include.__header:42", "source": {"index": 20, "namespace": "view", "name": "backend.include.__header", "file": "E:\\laragon\\www\\revoaibank\\resources\\views/backend/include/__header.blade.php", "line": 42}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fresources%2Fviews%2Fbackend%2Finclude%2F__header.blade.php:42", "ajax": false, "filename": "__header.blade.php", "line": "42"}, "connection": "revoaibank", "explain": null, "start_percent": 97.834, "width_percent": 2.166}]}, "models": {"data": {"App\\Models\\Notification": {"value": 39, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FNotification.php:1", "ajax": false, "filename": "Notification.php", "line": "?"}}, "App\\Models\\User": {"value": 5, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Currency": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FCurrency.php:1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FAdmin.php:1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php:1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 50, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 89, "messages": [{"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323827, "xdebug_link": null}, {"message": "[\n  ability => customer-mail-send,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1686115271 data-indent-pad=\"  \"><span class=sf-dump-note>customer-mail-send </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">customer-mail-send</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686115271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.375293, "xdebug_link": null}, {"message": "[\n  ability => customer-login,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-600580942 data-indent-pad=\"  \"><span class=sf-dump-note>customer-login </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">customer-login</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600580942\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.375671, "xdebug_link": null}, {"message": "[\n  ability => customer-balance-add-or-subtract,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1441535283 data-indent-pad=\"  \"><span class=sf-dump-note>customer-balance-add-or-subtract </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"32 characters\">customer-balance-add-or-subtract</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441535283\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376649, "xdebug_link": null}, {"message": "[\n  ability => customer-basic-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-481859234 data-indent-pad=\"  \"><span class=sf-dump-note>customer-basic-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">customer-basic-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481859234\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376881, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-985274155 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985274155\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.385397, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-851113422 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">subscribe-user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851113422\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.385848, "xdebug_link": null}, {"message": "[\n  ability => subscribe-user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-732846119 data-indent-pad=\"  \"><span class=sf-dump-note>subscribe-user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">subscribe-user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732846119\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.386247, "xdebug_link": null}, {"message": "[\n  ability => all-type-status,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-516922232 data-indent-pad=\"  \"><span class=sf-dump-note>all-type-status </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">all-type-status</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516922232\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.386495, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-111290551 data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">user-paybacks</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111290551\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.392505, "xdebug_link": null}, {"message": "[\n  ability => user-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1589355233 data-indent-pad=\"  \"><span class=sf-dump-note>user-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1589355233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.392968, "xdebug_link": null}, {"message": "[\n  ability => user-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1460629813 data-indent-pad=\"  \"><span class=sf-dump-note>user-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">user-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460629813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393392, "xdebug_link": null}, {"message": "[\n  ability => user-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2035757013 data-indent-pad=\"  \"><span class=sf-dump-note>user-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2035757013\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393794, "xdebug_link": null}, {"message": "[\n  ability => user-cards,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1169897939 data-indent-pad=\"  \"><span class=sf-dump-note>user-cards </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">user-cards</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169897939\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.396659, "xdebug_link": null}, {"message": "[\n  ability => transaction-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426263996 data-indent-pad=\"  \"><span class=sf-dump-note>transaction-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">transaction-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426263996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.396957, "xdebug_link": null}, {"message": "[\n  ability => support-ticket-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-608033726 data-indent-pad=\"  \"><span class=sf-dump-note>support-ticket-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">support-ticket-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-608033726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.397366, "xdebug_link": null}, {"message": "[\n  ability => customer-change-password,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1516967069 data-indent-pad=\"  \"><span class=sf-dump-note>customer-change-password </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">customer-change-password</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1516967069\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.40623, "xdebug_link": null}, {"message": "[\n  ability => customer-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1674928414 data-indent-pad=\"  \"><span class=sf-dump-note>customer-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674928414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.529559, "xdebug_link": null}, {"message": "[\n  ability => kyc-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-198863720 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">kyc-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198863720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.53008, "xdebug_link": null}, {"message": "[\n  ability => kyc-form-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-759908364 data-indent-pad=\"  \"><span class=sf-dump-note>kyc-form-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">kyc-form-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759908364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530431, "xdebug_link": null}, {"message": "[\n  ability => role-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-24786308 data-indent-pad=\"  \"><span class=sf-dump-note>role-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">role-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24786308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530693, "xdebug_link": null}, {"message": "[\n  ability => staff-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-586019291 data-indent-pad=\"  \"><span class=sf-dump-note>staff-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">staff-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586019291\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.530996, "xdebug_link": null}, {"message": "[\n  ability => wallet-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1435859591 data-indent-pad=\"  \"><span class=sf-dump-note>wallet-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">wallet-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435859591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.533961, "xdebug_link": null}, {"message": "[\n  ability => virtual-card-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1189504839 data-indent-pad=\"  \"><span class=sf-dump-note>virtual-card-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">virtual-card-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1189504839\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.534373, "xdebug_link": null}, {"message": "[\n  ability => user-paybacks-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>user-paybacks-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">user-paybacks-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.534801, "xdebug_link": null}, {"message": "[\n  ability => bank-profit,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>bank-profit </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bank-profit</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535084, "xdebug_link": null}, {"message": "[\n  ability => pending-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>pending-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">pending-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535335, "xdebug_link": null}, {"message": "[\n  ability => rejected-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-445940648 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">rejected-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-445940648\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535681, "xdebug_link": null}, {"message": "[\n  ability => all-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-503753751 data-indent-pad=\"  \"><span class=sf-dump-note>all-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">all-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-503753751\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535947, "xdebug_link": null}, {"message": "[\n  ability => allied-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>allied-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">allied-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536233, "xdebug_link": null}, {"message": "[\n  ability => others-bank-transfers,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-transfers </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">others-bank-transfers</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536627, "xdebug_link": null}, {"message": "[\n  ability => wire-transfer,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>wire-transfer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">wire-transfer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.536898, "xdebug_link": null}, {"message": "[\n  ability => others-bank-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>others-bank-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">others-bank-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537178, "xdebug_link": null}, {"message": "[\n  ability => dps-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>dps-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">dps-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537441, "xdebug_link": null}, {"message": "[\n  ability => ongoing-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-456992833 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-456992833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537721, "xdebug_link": null}, {"message": "[\n  ability => payable-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478568764 data-indent-pad=\"  \"><span class=sf-dump-note>payable-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">payable-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478568764\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538005, "xdebug_link": null}, {"message": "[\n  ability => complete-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-484107702 data-indent-pad=\"  \"><span class=sf-dump-note>complete-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">complete-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-484107702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538282, "xdebug_link": null}, {"message": "[\n  ability => closed-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1010593028 data-indent-pad=\"  \"><span class=sf-dump-note>closed-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010593028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538567, "xdebug_link": null}, {"message": "[\n  ability => all-dps,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-867830171 data-indent-pad=\"  \"><span class=sf-dump-note>all-dps </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-dps</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867830171\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.538848, "xdebug_link": null}, {"message": "[\n  ability => fdr-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1940756745 data-indent-pad=\"  \"><span class=sf-dump-note>fdr-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">fdr-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1940756745\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539169, "xdebug_link": null}, {"message": "[\n  ability => ongoing-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1177388407 data-indent-pad=\"  \"><span class=sf-dump-note>ongoing-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">ongoing-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177388407\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539561, "xdebug_link": null}, {"message": "[\n  ability => closed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-774853392 data-indent-pad=\"  \"><span class=sf-dump-note>closed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">closed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774853392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.539865, "xdebug_link": null}, {"message": "[\n  ability => completed-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-938123593 data-indent-pad=\"  \"><span class=sf-dump-note>completed-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">completed-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938123593\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540261, "xdebug_link": null}, {"message": "[\n  ability => all-fdr,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-323837422 data-indent-pad=\"  \"><span class=sf-dump-note>all-fdr </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">all-fdr</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323837422\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540562, "xdebug_link": null}, {"message": "[\n  ability => pending-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-278397502 data-indent-pad=\"  \"><span class=sf-dump-note>pending-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">pending-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278397502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.540887, "xdebug_link": null}, {"message": "[\n  ability => running-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328593221 data-indent-pad=\"  \"><span class=sf-dump-note>running-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">running-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328593221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54125, "xdebug_link": null}, {"message": "[\n  ability => due-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-578812513 data-indent-pad=\"  \"><span class=sf-dump-note>due-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">due-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-578812513\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541586, "xdebug_link": null}, {"message": "[\n  ability => paid-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-286970236 data-indent-pad=\"  \"><span class=sf-dump-note>paid-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">paid-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286970236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.541887, "xdebug_link": null}, {"message": "[\n  ability => rejected-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1395612330 data-indent-pad=\"  \"><span class=sf-dump-note>rejected-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">rejected-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395612330\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542191, "xdebug_link": null}, {"message": "[\n  ability => all-loan,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-467266763 data-indent-pad=\"  \"><span class=sf-dump-note>all-loan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">all-loan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-467266763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542526, "xdebug_link": null}, {"message": "[\n  ability => loan-plan-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766198916 data-indent-pad=\"  \"><span class=sf-dump-note>loan-plan-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">loan-plan-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766198916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542833, "xdebug_link": null}, {"message": "[\n  ability => bill-service-import,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1748231695 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-import </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">bill-service-import</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748231695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543127, "xdebug_link": null}, {"message": "[\n  ability => bill-convert-rate,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1767825329 data-indent-pad=\"  \"><span class=sf-dump-note>bill-convert-rate </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-convert-rate</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767825329\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543515, "xdebug_link": null}, {"message": "[\n  ability => bill-service-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-860528508 data-indent-pad=\"  \"><span class=sf-dump-note>bill-service-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">bill-service-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860528508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543812, "xdebug_link": null}, {"message": "[\n  ability => all-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2074314597 data-indent-pad=\"  \"><span class=sf-dump-note>all-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074314597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544109, "xdebug_link": null}, {"message": "[\n  ability => pending-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1973668372 data-indent-pad=\"  \"><span class=sf-dump-note>pending-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">pending-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1973668372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.544575, "xdebug_link": null}, {"message": "[\n  ability => complete-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-58914410 data-indent-pad=\"  \"><span class=sf-dump-note>complete-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">complete-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-58914410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545036, "xdebug_link": null}, {"message": "[\n  ability => return-bills,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-477593538 data-indent-pad=\"  \"><span class=sf-dump-note>return-bills </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">return-bills</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-477593538\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.545385, "xdebug_link": null}, {"message": "[\n  ability => automatic-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1438387162 data-indent-pad=\"  \"><span class=sf-dump-note>automatic-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">automatic-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438387162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54582, "xdebug_link": null}, {"message": "[\n  ability => manual-gateway-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1832036004 data-indent-pad=\"  \"><span class=sf-dump-note>manual-gateway-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manual-gateway-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832036004\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.546359, "xdebug_link": null}, {"message": "[\n  ability => deposit-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1473207243 data-indent-pad=\"  \"><span class=sf-dump-note>deposit-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">deposit-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473207243\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.546811, "xdebug_link": null}, {"message": "[\n  ability => training-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-326134728 data-indent-pad=\"  \"><span class=sf-dump-note>training-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">training-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326134728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547288, "xdebug_link": null}, {"message": "[\n  ability => training-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2090065228 data-indent-pad=\"  \"><span class=sf-dump-note>training-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">training-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2090065228\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547805, "xdebug_link": null}, {"message": "[\n  ability => withdraw-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2054560414 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">withdraw-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2054560414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548139, "xdebug_link": null}, {"message": "[\n  ability => withdraw-method-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1433721372 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-method-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">withdraw-method-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1433721372\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548461, "xdebug_link": null}, {"message": "[\n  ability => withdraw-schedule,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1097395904 data-indent-pad=\"  \"><span class=sf-dump-note>withdraw-schedule </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">withdraw-schedule</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097395904\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.548894, "xdebug_link": null}, {"message": "[\n  ability => referral-create,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-28698948 data-indent-pad=\"  \"><span class=sf-dump-note>referral-create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">referral-create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28698948\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549277, "xdebug_link": null}, {"message": "[\n  ability => manage-portfolio,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-294640796 data-indent-pad=\"  \"><span class=sf-dump-note>manage-portfolio </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage-portfolio</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294640796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549618, "xdebug_link": null}, {"message": "[\n  ability => reward-earning-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-992737278 data-indent-pad=\"  \"><span class=sf-dump-note>reward-earning-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">reward-earning-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-992737278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.549958, "xdebug_link": null}, {"message": "[\n  ability => reward-redeem-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1168615124 data-indent-pad=\"  \"><span class=sf-dump-note>reward-redeem-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">reward-redeem-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1168615124\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.550328, "xdebug_link": null}, {"message": "[\n  ability => site-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-21260530 data-indent-pad=\"  \"><span class=sf-dump-note>site-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">site-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21260530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.55072, "xdebug_link": null}, {"message": "[\n  ability => email-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1196656679 data-indent-pad=\"  \"><span class=sf-dump-note>email-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">email-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196656679\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551135, "xdebug_link": null}, {"message": "[\n  ability => language-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1632703971 data-indent-pad=\"  \"><span class=sf-dump-note>language-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">language-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632703971\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.55153, "xdebug_link": null}, {"message": "[\n  ability => page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-253554632 data-indent-pad=\"  \"><span class=sf-dump-note>page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253554632\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.551862, "xdebug_link": null}, {"message": "[\n  ability => plugin-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2104116099 data-indent-pad=\"  \"><span class=sf-dump-note>plugin-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">plugin-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104116099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.552203, "xdebug_link": null}, {"message": "[\n  ability => sms-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1313883067 data-indent-pad=\"  \"><span class=sf-dump-note>sms-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">sms-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313883067\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.552698, "xdebug_link": null}, {"message": "[\n  ability => push-notification-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-205556610 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">push-notification-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205556610\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553081, "xdebug_link": null}, {"message": "[\n  ability => notification-tune-setting,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1793311873 data-indent-pad=\"  \"><span class=sf-dump-note>notification-tune-setting </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"25 characters\">notification-tune-setting</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793311873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553479, "xdebug_link": null}, {"message": "[\n  ability => landing-page-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-244414540 data-indent-pad=\"  \"><span class=sf-dump-note>landing-page-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">landing-page-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244414540\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.553847, "xdebug_link": null}, {"message": "[\n  ability => custom-css,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1684118311 data-indent-pad=\"  \"><span class=sf-dump-note>custom-css </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">custom-css</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684118311\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.554261, "xdebug_link": null}, {"message": "[\n  ability => footer-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-27178740 data-indent-pad=\"  \"><span class=sf-dump-note>footer-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">footer-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27178740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.555163, "xdebug_link": null}, {"message": "[\n  ability => navigation-manage,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-921663705 data-indent-pad=\"  \"><span class=sf-dump-note>navigation-manage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">navigation-manage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921663705\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.55606, "xdebug_link": null}, {"message": "[\n  ability => subscriber-list,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-442939482 data-indent-pad=\"  \"><span class=sf-dump-note>subscriber-list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">subscriber-list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442939482\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.556432, "xdebug_link": null}, {"message": "[\n  ability => sms-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-755573123 data-indent-pad=\"  \"><span class=sf-dump-note>sms-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">sms-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755573123\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.556776, "xdebug_link": null}, {"message": "[\n  ability => email-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1118644391 data-indent-pad=\"  \"><span class=sf-dump-note>email-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">email-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1118644391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557147, "xdebug_link": null}, {"message": "[\n  ability => push-notification-template,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-995382332 data-indent-pad=\"  \"><span class=sf-dump-note>push-notification-template </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"26 characters\">push-notification-template</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995382332\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557545, "xdebug_link": null}, {"message": "[\n  ability => manage-cron-job,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1698209520 data-indent-pad=\"  \"><span class=sf-dump-note>manage-cron-job </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage-cron-job</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698209520\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.55803, "xdebug_link": null}, {"message": "[\n  ability => clear-cache,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-395651890 data-indent-pad=\"  \"><span class=sf-dump-note>clear-cache </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">clear-cache</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-395651890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558538, "xdebug_link": null}, {"message": "[\n  ability => application-details,\n  target => null,\n  result => true,\n  admin => 11,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>application-details </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">application-details</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>admin</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.558946, "xdebug_link": null}]}, "session": {"_token": "Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO", "_previous": "array:1 [\n  \"url\" => \"https://revoaibank.test/admin/user?page=1\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "array:1 [\n  \"intended\" => \"https://revoaibank.test/user/referral/tree\"\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "11", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "228", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/254/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit", "uri": "GET admin/user/{user}/edit", "controller": "App\\Http\\Controllers\\Backend\\UserController@edit<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Frevoaibank%2Fapp%2FHttp%2FControllers%2FBackend%2FUserController.php:199\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Backend/UserController.php:199-369</a>", "middleware": "web, auth:admin, XSS, isDemo, translate, trans, install_check", "duration": "544ms", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"718 characters\">XSRF-TOKEN=eyJpdiI6Ill2cG9yRk1rLzZ3WXRrdDc2ZEo4WGc9PSIsInZhbHVlIjoiTlRqQ2pzdVdINWFVMXVVZStiWkp6MWxRLzQzaUdyNUJKTVRLTnBsQTRnL2VoTTlXWk5QTmZsOWNrRWJYWktNTk1JaERoeTRXOTF5QnZ4WUxXd1grUWFabmV1UFlHblRUbm9HZkdIaTlQR29td2E5WHRObXZSYSthRHRJQmRRVzciLCJtYWMiOiI2MjM4OGI0MDgzZmM3ZDIzOTcwM2Y2OThlOGRjOWNjNTU0MDk5MjcxMjZhOGU4ZDRjOTg1MWNmNmU5M2NhMWUzIiwidGFnIjoiIn0%3D; digital_bank_session=eyJpdiI6Ikp5WVRlRVhja1BXSmdqbnFFLzhQT3c9PSIsInZhbHVlIjoiSlhZS2JSOVNjZzdzOHNlclM4ZitTemRjUHJ5T3llV3hTcEhlWjNyZTJwVUkzY1Zzc2ZmUkpmUWFEWDNtWDE0MktsdXpGaU5JQTNzWkJBNlV2UjhnU2FCdWNlSmVxR0dFdmxkS0tHcnRTdmNJMlBjY0pJbWgrQW0vbDNib1phVkIiLCJtYWMiOiI5NTdhYjNkMmEyMjU0ZmIyNGU1MDZiNzI1ZGRjNzQ4NDBmNzE5YmZmZGZjMDM0Yzc3ODdhZGU1Mzg2ZjExZjMzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/admin/user?page=1&amp;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">revoaibank.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>digital_bank_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LQmddLR34V5gx8fxxD8s2W20y4FntkShwkOAEGlR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 04:52:24 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Kvseh6CaCKUXmvPa97kiNo1IzNKNPZaw3nv8RXkO</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://revoaibank.test/admin/user?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://revoaibank.test/user/referral/tree</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>11</span>\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>228</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://revoaibank.test/admin/user/254/edit", "action_name": "admin.user.edit", "controller_action": "App\\Http\\Controllers\\Backend\\UserController@edit"}, "badge": null}}